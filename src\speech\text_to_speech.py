"""
Text-to-Speech implementation using MeloTTS with improved error handling and architecture.
"""
import torch
import sounddevice as sd
import numpy as np
import time
from typing import Optional, List, Dict, Any, Union
from dataclasses import dataclass
from enum import Enum
import logging
from src.utils.logger import get_logger
logger = get_logger(__name__)

# Configuration constants
DEFAULT_MODEL_NAME = "myshell-ai/MeloTTS-English"
DEFAULT_DEVICE = "auto"
DEFAULT_SPEED = 1.0
DEFAULT_VOLUME = 1.0
SPEED_RANGE = (0.1, 3.0)
VOLUME_RANGE = (0.0, 1.0)


class TTSError(Exception):
    """Base exception for TTS operations."""
    pass


class ModelLoadError(TTSError):
    """Raised when model fails to load."""
    pass


class SynthesisError(TTSError):
    """Raised when speech synthesis fails."""
    pass


class VoiceError(TTSError):
    """Raised when voice selection fails."""
    pass


class AudioDevice(Enum):
    """Supported audio devices."""
    AUTO = "auto"
    CPU = "cpu"
    CUDA = "cuda"


@dataclass
class Voice:
    """Represents a TTS voice with its properties."""
    id: str
    name: str
    language: str
    gender: Optional[str] = None
    accent: Optional[str] = None

    def __str__(self) -> str:
        parts = [self.name, f"({self.language})"]
        if self.gender:
            parts.append(f"[{self.gender}]")
        if self.accent:
            parts.append(f"<{self.accent}>")
        return " ".join(parts)


@dataclass
class TTSConfig:
    """Configuration for TTS engine."""
    model_name: str = DEFAULT_MODEL_NAME
    device: str = DEFAULT_DEVICE
    speed: float = DEFAULT_SPEED
    volume: float = DEFAULT_VOLUME
    
    def __post_init__(self):
        self.validate()
    
    def validate(self):
        """Validate configuration parameters."""
        if not SPEED_RANGE[0] <= self.speed <= SPEED_RANGE[1]:
            raise ValueError(f"Speed must be between {SPEED_RANGE[0]} and {SPEED_RANGE[1]}")
        if not VOLUME_RANGE[0] <= self.volume <= VOLUME_RANGE[1]:
            raise ValueError(f"Volume must be between {VOLUME_RANGE[0]} and {VOLUME_RANGE[1]}")


class MeloTTSEngine:
    """
    MeloTTS-based text-to-speech engine with proper error handling and voice management.
    """
    
    def __init__(self, config: Optional[TTSConfig] = None):
        """Initialize the TTS engine."""
        self.config = config or TTSConfig()
        self.device = self._resolve_device()
        self.model = None
        self.processor = None
        self._voices: List[Voice] = []
        self._current_voice: Optional[Voice] = None
        self._is_loaded = False
        
        logger.info(f"Initializing MeloTTS engine with device: {self.device}")
        
    def _resolve_device(self) -> str:
        """Resolve the actual device to use."""
        if self.config.device == AudioDevice.AUTO.value:
            return "cuda" if torch.cuda.is_available() else "cpu"
        return self.config.device
    
    def load_model(self) -> None:
        """Load the MeloTTS model and processor."""
        if self._is_loaded:
            logger.debug("Model already loaded")
            return
            
        try:
            logger.info(f"Loading model: {self.config.model_name}")
            
            # Import here to avoid issues if transformers isn't installed
            from transformers import AutoProcessor, AutoModelForTextToSpeech
            
            # Load processor and model
            self.processor = AutoProcessor.from_pretrained(self.config.model_name)
            self.model = AutoModelForTextToSpeech.from_pretrained(self.config.model_name)
            
            # Move model to device and set to eval mode
            self.model.to(self.device)
            self.model.eval()
            
            # Load available voices
            self._load_voices()
            
            self._is_loaded = True
            logger.info("Model loaded successfully")
            
        except ImportError as e:
            raise ModelLoadError("transformers library is required but not installed") from e
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise ModelLoadError(f"Failed to load model '{self.config.model_name}': {e}") from e
    
    def _load_voices(self) -> None:
        """Load available voices from the model."""
        # Define common MeloTTS voices
        # In a real implementation, these would be loaded from model config
        voice_definitions = [
            Voice("EN-US", "English (US)", "en-US", "neutral"),
            Voice("EN-BR", "English (British)", "en-GB", "neutral", "British"),
            Voice("EN-AU", "English (Australian)", "en-AU", "neutral", "Australian"),
            Voice("EN-IN", "English (Indian)", "en-IN", "neutral", "Indian"),
            Voice("ES-ES", "Spanish (Spain)", "es-ES", "neutral"),
            Voice("ES-MX", "Spanish (Mexico)", "es-MX", "neutral", "Mexican"),
            Voice("FR-FR", "French (France)", "fr-FR", "neutral"),
            Voice("DE-DE", "German (Germany)", "de-DE", "neutral"),
            Voice("IT-IT", "Italian (Italy)", "it-IT", "neutral"),
            Voice("PT-BR", "Portuguese (Brazil)", "pt-BR", "neutral"),
            Voice("RU-RU", "Russian (Russia)", "ru-RU", "neutral"),
            Voice("JA-JP", "Japanese (Japan)", "ja-JP", "neutral"),
            Voice("KO-KR", "Korean (Korea)", "ko-KR", "neutral"),
            Voice("ZH-CN", "Chinese (Mandarin)", "zh-CN", "neutral"),
        ]
        
        # Filter voices based on what the model actually supports
        # This is a simplified approach - in reality, you'd query the model
        self._voices = voice_definitions
        
        # Set default voice
        if self._voices:
            self._current_voice = self._voices[0]
            logger.info(f"Default voice set to: {self._current_voice}")
    
    def get_voices(self) -> List[Voice]:
        """Get list of available voices."""
        if not self._is_loaded:
            self.load_model()
        return self._voices.copy()
    
    def set_voice(self, voice_id: Optional[str] = None, language: Optional[str] = None, 
                  gender: Optional[str] = None) -> bool:
        """
        Set the current voice.
        
        Args:
            voice_id: Specific voice ID
            language: Language code (e.g., 'en-US')
            gender: Gender preference ('male', 'female', 'neutral')
            
        Returns:
            True if voice was set successfully
        """
        if not self._is_loaded:
            self.load_model()
        
        # Try to find voice by ID first
        if voice_id:
            for voice in self._voices:
                if voice.id == voice_id:
                    self._current_voice = voice
                    logger.info(f"Voice set to: {voice}")
                    return True
        
        # Try to find by language
        if language:
            for voice in self._voices:
                if voice.language.lower() == language.lower():
                    self._current_voice = voice
                    logger.info(f"Voice set to: {voice}")
                    return True
        
        # Try to find by gender
        if gender:
            for voice in self._voices:
                if voice.gender and voice.gender.lower() == gender.lower():
                    self._current_voice = voice
                    logger.info(f"Voice set to: {voice}")
                    return True
        
        logger.warning("No matching voice found")
        return False
    
    def synthesize(self, text: str) -> np.ndarray:
        """
        Synthesize speech from text.
        
        Args:
            text: Text to synthesize
            
        Returns:
            Audio waveform as numpy array
        """
        if not self._is_loaded:
            self.load_model()
        
        if not text or not isinstance(text, str):
            raise ValueError("Text must be a non-empty string")
        
        try:
            logger.info(f"Synthesizing: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            start_time = time.perf_counter()
            
            # Prepare inputs
            inputs = self.processor(
                text=text,
                return_tensors="pt",
                padding=True,
                truncation=True
            ).to(self.device)
            
            # Add voice/language info if available
            if self._current_voice:
                # This is model-specific - adjust based on actual MeloTTS API
                inputs["speaker_id"] = self._current_voice.id
            
            # Generate speech
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    do_sample=True,
                    temperature=0.7,
                    max_length=8192,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            # Convert to audio waveform
            # This is simplified - actual implementation depends on model output format
            audio_waveform = outputs.cpu().numpy().squeeze()
            
            # Apply speed adjustment
            if self.config.speed != 1.0:
                # Simple time-stretching (in practice, use librosa or similar)
                target_length = int(len(audio_waveform) / self.config.speed)
                audio_waveform = np.interp(
                    np.linspace(0, len(audio_waveform) - 1, target_length),
                    np.arange(len(audio_waveform)),
                    audio_waveform
                )
            
            # Apply volume adjustment
            if self.config.volume != 1.0:
                audio_waveform = audio_waveform * self.config.volume
                audio_waveform = np.clip(audio_waveform, -1.0, 1.0)
            
            duration = time.perf_counter() - start_time
            audio_duration = len(audio_waveform) / self.get_sample_rate()
            logger.info(f"Synthesis completed in {duration:.2f}s (audio: {audio_duration:.2f}s)")
            
            return audio_waveform
            
        except Exception as e:
            logger.error(f"Synthesis failed: {e}")
            raise SynthesisError(f"Failed to synthesize text: {e}") from e
    
    def speak(self, text: str, blocking: bool = True) -> None:
        """
        Synthesize and play text.
        
        Args:
            text: Text to speak
            blocking: Whether to wait for playback to complete
        """
        try:
            audio_waveform = self.synthesize(text)
            sample_rate = self.get_sample_rate()
            
            if len(audio_waveform) == 0:
                logger.warning("Empty audio generated")
                return
            
            logger.info("Playing audio...")
            sd.play(audio_waveform, samplerate=sample_rate)
            
            if blocking:
                sd.wait()
                logger.info("Playback completed")
            
        except Exception as e:
            logger.error(f"Playback failed: {e}")
            raise SynthesisError(f"Failed to play audio: {e}") from e
    
    def get_sample_rate(self) -> int:
        """Get the model's sample rate."""
        if not self._is_loaded:
            self.load_model()
        
        # Default sample rate for MeloTTS
        return getattr(self.model.config, 'sampling_rate', 22050)
    
    def update_config(self, **kwargs) -> None:
        """Update configuration parameters."""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
            else:
                raise ValueError(f"Unknown configuration parameter: {key}")
        
        self.config.validate()
        logger.info(f"Configuration updated: {kwargs}")


class TextToSpeech:
    """
    High-level text-to-speech interface with simplified API.
    """
    
    def __init__(self, model_name: str = DEFAULT_MODEL_NAME, 
                 device: str = DEFAULT_DEVICE, **kwargs):
        """Initialize TTS with simplified parameters."""
        config = TTSConfig(model_name=model_name, device=device, **kwargs)
        self.engine = MeloTTSEngine(config)
    
    def speak(self, text: str) -> None:
        """Speak the given text."""
        self.engine.speak(text)
    
    def set_voice(self, voice_id: Optional[str] = None, **kwargs) -> bool:
        """Set voice with simplified interface."""
        return self.engine.set_voice(voice_id, **kwargs)
    
    def set_speed(self, speed: float) -> None:
        """Set speech speed."""
        self.engine.update_config(speed=speed)
    
    def set_volume(self, volume: float) -> None:
        """Set speech volume."""
        self.engine.update_config(volume=volume)
    
    def get_voices(self) -> List[Dict[str, str]]:
        """Get available voices as simple dictionaries."""
        voices = self.engine.get_voices()
        return [
            {
                "id": voice.id,
                "name": voice.name,
                "language": voice.language,
                "gender": voice.gender or "neutral"
            }
            for voice in voices
        ]


# Example usage and testing
if __name__ == "__main__":
    print("=== MeloTTS Text-to-Speech Demo ===")
    
    try:
        # Initialize TTS
        tts = TextToSpeech()
        
        # Basic speech
        print("\n1. Basic speech test:")
        tts.speak("Hello! This is a test of the MeloTTS text-to-speech system.")
        
        # List available voices
        print("\n2. Available voices:")
        voices = tts.get_voices()
        for i, voice in enumerate(voices[:5]):  # Show first 5
            print(f"   {i+1}. {voice['name']} ({voice['id']}) - {voice['language']}")
        
        # Test voice selection
        print("\n3. Testing voice selection:")
        if tts.set_voice("EN-BR"):
            tts.speak("Now I'm speaking with a British accent!")
        
        # Test speed and volume
        print("\n4. Testing speed and volume:")
        tts.set_speed(0.8)
        tts.set_volume(0.7)
        tts.speak("I'm speaking more slowly and quietly now.")
        
        # Reset to defaults
        tts.set_speed(1.0)
        tts.set_volume(1.0)
        
        print("\n5. Demo completed successfully!")
        
    except TTSError as e:
        print(f"TTS Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()