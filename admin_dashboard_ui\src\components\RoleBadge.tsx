import { Role } from '../types';

const roleColors: Record<string, string> = {
  SUPERADMIN: 'bg-purple-600',
  ADMIN: 'bg-blue-600',
  HR_LEAD: 'bg-green-600',
  VIEWER: 'bg-gray-500',
  SUPPORT_AGENT: 'bg-yellow-600',
  COMPLIANCE_AUDITOR: 'bg-red-600',
  DEVELOPER: 'bg-pink-600',
  superadmin: 'bg-purple-600',
  admin: 'bg-blue-600',
  hr_lead: 'bg-green-600',
  viewer: 'bg-gray-500',
  support_agent: 'bg-yellow-600',
  compliance_auditor: 'bg-red-600',
  developer: 'bg-pink-600',
};

export function RoleBadge({ role }: { role: string }) {
  return (
    <span className={`px-2 py-1 rounded text-white text-xs font-semibold ${roleColors[role] || 'bg-gray-400'}`}>{role}</span>
  );
} 