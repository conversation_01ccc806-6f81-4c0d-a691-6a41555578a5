import React from 'react';
import { FileAttachment } from '@/types';

interface FilePreviewModalProps {
  onClose: () => void;
  file: FileAttachment | null;
}

const FilePreviewModal: React.FC<FilePreviewModalProps> = ({ onClose, file }) => {
  if (!file) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const renderPreview = () => {
    if (!file.url) {
      return (
        <div className="text-center py-8">
          <i className="fas fa-file text-4xl text-primary-text-secondary dark:text-dark-text-secondary mb-4"></i>
          <p className="text-primary-text-secondary dark:text-dark-text-secondary">
            Preview not available
          </p>
        </div>
      );
    }

    // Handle different file types
    if (file.type.startsWith('image/')) {
      return (
        <img 
          src={file.url} 
          alt={file.name}
          className="max-w-full max-h-96 object-contain mx-auto"
        />
      );
    }

    if (file.type === 'application/pdf') {
      return (
        <iframe
          src={file.url}
          className="w-full h-96 border border-primary-border dark:border-dark-border rounded"
          title={file.name}
        />
      );
    }

    if (file.type.includes('text/') || file.name.endsWith('.md')) {
      return (
        <div className="bg-primary-secondary dark:bg-dark-secondary p-4 rounded border border-primary-border dark:border-dark-border">
          <pre className="text-sm text-primary-text dark:text-dark-text whitespace-pre-wrap">
            {file.preview || 'Text preview not available'}
          </pre>
        </div>
      );
    }

    // Default preview for unsupported types
    return (
      <div className="text-center py-8">
        <i className="fas fa-file text-4xl text-primary-text-secondary dark:text-dark-text-secondary mb-4"></i>
        <p className="text-primary-text dark:text-dark-text mb-2">
          {file.name}
        </p>
        <p className="text-primary-text-secondary dark:text-dark-text-secondary">
          Preview not available for this file type
        </p>
        {file.url && (
          <a
            href={file.url}
            download={file.name}
            className="inline-block mt-4 btn-primary"
          >
            <i className="fas fa-download mr-2"></i>
            Download
          </a>
        )}
      </div>
    );
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-primary-border dark:border-dark-border">
          <h3 className="text-lg font-semibold text-primary-text dark:text-dark-text truncate">
            {file.name}
          </h3>
          <button onClick={onClose} className="icon-btn">
            <i className="fas fa-times text-primary-text-secondary dark:text-dark-text-secondary"></i>
          </button>
        </div>

        {/* Body */}
        <div className="p-6">
          {renderPreview()}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-primary-border dark:border-dark-border">
          <button onClick={onClose} className="btn-secondary">
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilePreviewModal;
