import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import toast from 'react-hot-toast';

interface RoleOption {
  id: number;
  name: string;
  level: number;
}

interface EditRoleModalProps {
  user: { id: number; full_name: string; role: string; role_level: number };
  onClose: () => void;
  onSave: (userId: number, newRole: string) => void;
  canAssignRole: (roleLevel: number) => boolean;
}

const EditRoleModal: React.FC<EditRoleModalProps> = ({ user, onClose, onSave, canAssignRole }) => {
  const [roles, setRoles] = useState<RoleOption[]>([]);
  const [roleId, setRoleId] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [roleName, setRoleName] = useState<string>("");

  useEffect(() => {
    async function fetchRoles() {
      try {
        const res = await fetch('/api/roles');
        if (!res.ok) throw new Error('Failed to fetch roles');
        const data = await res.json();
        setRoles(data);
        const current = data.find((r: RoleOption) => r.name === user.role);
        setRoleId(current ? current.id : 0);
        setRoleName(current ? current.name : "");
      } catch (e: any) {
        toast.error(e.message);
      }
    }
    fetchRoles();
  }, [user.role]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!roleId) {
      toast.error('Please select a role');
      return;
    }
    setLoading(true);
    try {
      const selectedRole = roles.find(r => r.id === roleId);
      await onSave(user.id, selectedRole ? selectedRole.name : "");
      toast.success('Role updated successfully');
    } catch (e: any) {
      toast.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
      <div className="bg-white dark:bg-gray-900 rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-bold mb-4">Edit Role for {user.full_name}</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block mb-1">Role</label>
            <select className="w-full border rounded px-2 py-1" value={roleId} onChange={e => setRoleId(Number(e.target.value))} required>
              <option value="" disabled>Select role</option>
              {roles.filter(r => canAssignRole(r.level)).map(role => (
                <option key={role.id} value={role.id}>{role.name}</option>
              ))}
            </select>
          </div>
          <div className="flex justify-end gap-2 mt-4">
            <Button type="button" variant="secondary" onClick={onClose}>Cancel</Button>
            <Button type="submit" disabled={loading}>{loading ? 'Saving...' : 'Save'}</Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditRoleModal; 