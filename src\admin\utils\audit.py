from src.admin.models import AuditLog
from datetime import datetime

def log_audit_action(db, actor_id, target_user_id, action, details, ip_address, tenant_id=None):
    log = AuditLog(
        created_by=actor_id,
        target_user=target_user_id,
        action=action,
        details=details,
        ip_address=ip_address,
        tenant_id=tenant_id,
        timestamp=datetime.utcnow()
    )
    db.add(log)
    db.commit()
    return log 