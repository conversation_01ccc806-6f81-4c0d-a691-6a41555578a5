import React, { useState } from "react";
import { motion } from "framer-motion";
import useS<PERSON> from "swr";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { LiveIndicator } from "@/components/LiveIndicator";
import { ExportButton } from "@/components/ExportButton";
import { TrendingUp, TrendingDown, Minus, Users, MessageSquare, Heart, HelpCircle } from "lucide-react";
import MotionDiv from '../../../components/ui/MotionDiv';

const TopIntentsBarChart = React.lazy(() => import("./components/TopIntentsBarChart"));
const TopicTrendsLineChart = React.lazy(() => import("./components/TopicTrendsLineChart"));
const SentimentPieChart = React.lazy(() => import("./components/SentimentPieChart"));
const TopQuestionsTable = React.lazy(() => import("./components/TopQuestionsTable"));

const fetcher = (url: string) => fetch(url).then(res => res.json());

const KPI_CARDS = [
  {
    key: "total_queries",
    label: "Total Queries Today",
    icon: MessageSquare,
    color: "text-blue-600 dark:text-blue-400",
    bgColor: "bg-blue-50 dark:bg-blue-900/20"
  },
  {
    key: "avg_sentiment",
    label: "Avg. Sentiment",
    icon: Heart,
    color: "text-green-600 dark:text-green-400",
    bgColor: "bg-green-50 dark:bg-green-900/20"
  },
  {
    key: "active_users",
    label: "Active Users",
    icon: Users,
    color: "text-purple-600 dark:text-purple-400",
    bgColor: "bg-purple-50 dark:bg-purple-900/20"
  },
  {
    key: "unique_questions",
    label: "Unique Questions",
    icon: HelpCircle,
    color: "text-orange-600 dark:text-orange-400",
    bgColor: "bg-orange-50 dark:bg-orange-900/20"
  },
];

const INSIGHTS_TOPICS = ["Leave Balance", "Payslip", "Attendance"];

const Insights: React.FC = () => {
  const { data, error, isLoading } = useSWR("/api/chat-analytics/live", fetcher, { refreshInterval: 180000 });
  console.log("INSIGHTS DATA", { data, error, isLoading });
  const [tab, setTab] = useState("intents");

  // Extract analytics data
  const topIntents = data?.top_intents || [];
  const trendingTopics = data?.trending_topics || [];
  const sentimentDistribution = data?.sentiment_distribution || [];
  const topQuestions = data?.top_questions || [];

  // Debug logs for chart/table props
  console.log("TopIntentsBarChart props", { topIntents, isLoading, error });
  console.log("TopicTrendsLineChart props", { trendingTopics, INSIGHTS_TOPICS, isLoading, error });
  console.log("SentimentPieChart props", { sentimentDistribution, isLoading, error });
  console.log("TopQuestionsTable props", { topQuestions, isLoading, error });

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 dark:from-gray-100 dark:to-gray-400 bg-clip-text text-transparent">
            Analytics Insights
          </h1>
          <p className="text-base text-muted-foreground">
            Real-time analytics and performance metrics from your chatbot
          </p>
        </div>
        <div className="flex items-center gap-2">
          <LiveIndicator />
          <ExportButton />
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
        {isLoading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-20 w-full max-w-[220px] rounded-xl mx-auto" shimmer />
          ))
        ) : error ? (
          <div className="col-span-4 text-red-500 p-4 text-center rounded-xl bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
            <p className="font-medium">Failed to load KPIs</p>
            <p className="text-sm mt-1 opacity-75">Please try refreshing the page</p>
          </div>
        ) : (
          KPI_CARDS.map((card, index) => {
            const IconComponent = card.icon;
            const value = data?.[card.key] ?? "-";
            const trend = Math.random() > 0.5 ? "up" : "down"; // Mock trend data
            const trendValue = Math.floor(Math.random() * 20) + 1;

            return (
              <Card
                key={card.key}
                className="group relative overflow-hidden border-0 bg-gradient-to-br from-white via-white to-gray-50/50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800/50 max-w-[220px] mx-auto p-2"
                hover={true}
                animate={true}
                delay={index * 0.1}
              >
                <CardContent className="p-2">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-2">
                        <div className={`p-1 rounded-md ${card.bgColor}`}>
                          <IconComponent className={`h-4 w-4 ${card.color}`} />
                        </div>
                        <span className="text-xs font-medium text-muted-foreground">
                          {card.label}
                        </span>
                      </div>
                      <div className="space-y-0.5">
                        <span className="text-lg font-bold tracking-tight">
                          {value}
                        </span>
                        <div className="flex items-center gap-1 text-xs">
                          {trend === "up" ? (
                            <TrendingUp className="h-3 w-3 text-green-500" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-500" />
                          )}
                          <span className={trend === "up" ? "text-green-600" : "text-red-600"}>
                            {trendValue}% vs last week
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Analytics Tabs */}
      <div>
        <Tabs value={tab} onValueChange={setTab} className="w-full">
          <TabsList className="mb-4 grid w-full grid-cols-4 lg:w-fit lg:grid-cols-4">
            <TabsTrigger value="intents" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              Top Intents
            </TabsTrigger>
            <TabsTrigger value="topics" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              Trending Topics
            </TabsTrigger>
            <TabsTrigger value="sentiment" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              Sentiment
            </TabsTrigger>
            <TabsTrigger value="questions" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              Top Questions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="intents">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 w-full">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <div className="p-1 rounded-md bg-blue-50 dark:bg-blue-900/20">
                    <MessageSquare className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  Top User Intents
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Most frequently detected user intentions and their sample queries
                </p>
              </CardHeader>
              <CardContent className="p-2">
                <TopIntentsBarChart topIntents={topIntents} loading={isLoading} error={error} height={180} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="topics">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <div className="p-1 rounded-md bg-green-50 dark:bg-green-900/20">
                    <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  Trending Topics
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Topic popularity trends over time across different categories
                </p>
              </CardHeader>
              <CardContent className="p-2">
                <TopicTrendsLineChart trendingTopics={trendingTopics} topics={INSIGHTS_TOPICS} loading={isLoading} error={error} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sentiment">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 w-full">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <div className="p-1 rounded-md bg-purple-50 dark:bg-purple-900/20">
                    <Heart className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  Sentiment Analysis
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Distribution of user sentiment across all interactions
                </p>
              </CardHeader>
              <CardContent className="p-2">
                <SentimentPieChart sentimentDistribution={sentimentDistribution} loading={isLoading} error={error} height={180} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="questions">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <div className="p-1 rounded-md bg-orange-50 dark:bg-orange-900/20">
                    <HelpCircle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  Frequently Asked Questions
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Most common questions and their frequency of occurrence
                </p>
              </CardHeader>
              <CardContent className="p-2">
                <TopQuestionsTable topQuestions={topQuestions} loading={isLoading} error={error} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Insights; 