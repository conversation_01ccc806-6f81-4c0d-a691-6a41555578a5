import React, { useEffect, useState } from 'react';
import { usePermissions } from '../hooks/usePermissions';
import { Button } from './ui/button';
import AddUserModal from './modals/AddUserModal';
import EditRoleModal from './modals/EditRoleModal';
import DeleteUserDialog from './modals/DeleteUserDialog';
import toast, { Toaster } from 'react-hot-toast';

interface AdminUser {
  id: number;
  full_name: string;
  email: string;
  role: string;
  role_level: number;
  tenant_id?: string | null;
  created_at?: string;
  updated_at?: string;
  active?: number | null;
}

const AdminUsersRoles: React.FC = () => {
  const { hasPermission, canAssignRole } = usePermissions();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editUser, setEditUser] = useState<AdminUser | null>(null);
  const [deleteUser, setDeleteUser] = useState<AdminUser | null>(null);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/admin-users');
      if (!res.ok) throw new Error('Failed to fetch users');
      const data = await res.json();
      setUsers(data.users || []);
    } catch (e: any) {
      setError(e.message);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // Handlers for add, edit, delete
  const handleAddUser = async (userData: any) => {
    try {
      const res = await fetch('/api/admin-users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });
      if (!res.ok) {
        const error = await res.json();
        if (error.detail === 'Admin already exists') {
          toast.error('Admin user with this email already exists.');
        } else {
          toast.error('Failed to add user');
        }
        return;
      }
      toast.success('User added successfully');
      setShowAddModal(false);
      fetchUsers();
    } catch (e: any) {
      toast.error(e.message);
    }
  };

  const handleEditRole = async (userId: number, newRole: string) => {
    try {
      const res = await fetch(`/api/admin-users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ role: newRole }),
      });
      if (!res.ok) throw new Error('Failed to update role');
      toast.success('Role updated successfully');
      setEditUser(null);
      fetchUsers();
    } catch (e: any) {
      toast.error(e.message);
    }
  };

  const handleDeleteUser = async (userId: number) => {
    try {
      const res = await fetch(`/api/admin-users/${userId}`, {
        method: 'DELETE' });
      if (!res.ok) {
        const error = await res.json();
        toast.error(error.detail || 'Failed to delete user');
        setDeleteUser(null);
        return;
      }
      toast.success('User removed successfully');
      setDeleteUser(null);
      fetchUsers();
    } catch (e: any) {
      toast.error(e.message);
      setDeleteUser(null);
    }
  };

  return (
    <div className="p-6">
      <Toaster position="top-right" />
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Admin Users & Roles</h2>
        {hasPermission('manage_users') && (
          <Button onClick={() => setShowAddModal(true)}>Add User</Button>
        )}
      </div>
      {loading ? (
        <div>Loading...</div>
      ) : error ? (
        <div className="text-red-500">{error}</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg">
            <thead>
              <tr>
                <th className="px-4 py-2">Name</th>
                <th className="px-4 py-2">Email</th>
                <th className="px-4 py-2">Role</th>
                <th className="px-4 py-2">Tenant</th>
                <th className="px-4 py-2">Active</th>
                <th className="px-4 py-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id} className="border-t border-gray-200 dark:border-gray-700">
                  <td className="px-4 py-2">{user.full_name}</td>
                  <td className="px-4 py-2">{user.email}</td>
                  <td className="px-4 py-2">{user.role?.toUpperCase()}</td>
                  <td className="px-4 py-2">{user.tenant_id ?? '-'}</td>
                  <td className="px-4 py-2">{user.active === 1 ? 'Yes' : 'No'}</td>
                  <td className="px-4 py-2 flex gap-2">
                    {hasPermission('manage_users') && (
                      <>
                        <Button size="sm" onClick={() => setEditUser(user)}>Edit</Button>
                        <Button size="sm" variant="destructive" onClick={() => setDeleteUser(user)}>Remove</Button>
                      </>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Add User Modal */}
      {showAddModal && (
        <AddUserModal
          onClose={() => setShowAddModal(false)}
          onAdd={handleAddUser}
          canAssignRole={canAssignRole}
        />
      )}

      {/* Edit Role Modal */}
      {editUser && (
        <EditRoleModal
          user={editUser}
          onClose={() => setEditUser(null)}
          onSave={handleEditRole}
          canAssignRole={canAssignRole}
        />
      )}

      {/* Delete User Dialog */}
      {deleteUser && (
        <DeleteUserDialog
          user={deleteUser}
          onClose={() => setDeleteUser(null)}
          onDelete={handleDeleteUser}
        />
      )}
    </div>
  );
};

export default AdminUsersRoles; 