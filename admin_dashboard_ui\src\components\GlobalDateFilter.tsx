import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Calendar, ChevronDown } from "lucide-react";
import { DateRangePicker } from "@/components/ui/date-range-picker";

interface GlobalDateFilterProps {
  dateRange: string;
  onDateRangeChange: (range: string) => void;
  onCustomRangeChange?: (startDate: Date, endDate: Date) => void;
  className?: string;
}

type DateRange = {
  from: Date | undefined;
  to: Date | undefined;
};

const dateRangeOptions = [
  { label: "Last 7 Days", value: "7d" },
  { label: "Last 30 Days", value: "30d" },
  { label: "This Month", value: "month" },
  { label: "Last Month", value: "lastMonth" },
  { label: "This Quarter", value: "quarter" },
  { label: "This Year", value: "year" },
  { label: "Custom Range", value: "custom" },
];

export const GlobalDateFilter: React.FC<GlobalDateFilterProps> = ({
  dateRange,
  onDateRangeChange,
  onCustomRangeChange,
  className = "",
}) => {
  const [isCustomOpen, setIsCustomOpen] = useState(false);
  const [customRange, setCustomRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });

  const getCurrentLabel = () => {
    const option = dateRangeOptions.find(opt => opt.value === dateRange);
    return option?.label || "Select Date Range";
  };

  const handleRangeSelect = (value: string) => {
    if (value === "custom") {
      setIsCustomOpen(true);
    } else {
      onDateRangeChange(value);
      setIsCustomOpen(false);
    }
  };

  const handleCustomRangeChange = (range: DateRange) => {
    setCustomRange(range);
    if (range.from && range.to && onCustomRangeChange) {
      onCustomRangeChange(range.from, range.to);
      onDateRangeChange("custom");
      setIsCustomOpen(false);
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Calendar className="h-4 w-4 text-muted-foreground" />
      <DropdownMenu open={isCustomOpen} onOpenChange={setIsCustomOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="min-w-[180px] justify-between">
            <span className="text-sm">{getCurrentLabel()}</span>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          {dateRangeOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() => handleRangeSelect(option.value)}
              className={dateRange === option.value ? "bg-accent" : ""}
            >
              {option.label}
              {dateRange === option.value && (
                <span className="ml-auto text-xs">✓</span>
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Custom Date Range Picker */}
      {isCustomOpen && (
        <div className="absolute top-full mt-2 z-50">
          <DateRangePicker
            value={customRange}
            onChange={handleCustomRangeChange}
            className="bg-popover border rounded-lg shadow-lg p-3"
          />
        </div>
      )}
    </div>
  );
}; 