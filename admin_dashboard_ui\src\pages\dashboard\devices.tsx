import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getDeviceLogs } from "@/services/api";
import { DeviceLog } from '../../types';
import { Layout } from '../../components/Layout';
import { ProtectedRoute } from '../../components/ProtectedRoute';

function getLocation(log: DeviceLog) {
  if (!log.country && !log.city && !log.region) return '-';
  return [log.city, log.region, log.country].filter(Boolean).join(', ');
}

export default function DevicesPage() {
  const [email, setEmail] = useState('');
  const [showNew, setShowNew] = useState(false);
  const [expanded, setExpanded] = useState<number | null>(null);
  const [page, setPage] = useState(1);
  const pageSize = 20;

  const { data, isLoading, error } = useQuery({
    queryKey: ['deviceLogs'],
    queryFn: async () => {
      try {
        const res = await getDeviceLogs();
        return res.data.devices as DeviceLog[];
      } catch {
        return [];
      }
    },
  });

  // Assume new_device = first_seen === last_seen
  const filtered = (data || []).filter((log: any) =>
    (!email || log.email?.includes(email)) &&
    (!showNew || log.first_seen === log.last_seen)
  );
  const paginated = filtered.slice((page - 1) * pageSize, page * pageSize);
  const totalPages = Math.ceil(filtered.length / pageSize);

  return (
    <ProtectedRoute roles={['SUPERADMIN']}>
      <h1 className="text-2xl font-bold mb-6">Device Logins</h1>
      <div className="flex gap-4 mb-4">
        <input
          type="text"
          placeholder="Filter by email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="border rounded px-2 py-1"
        />
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={showNew}
            onChange={e => setShowNew(e.target.checked)}
          />
          Show new devices only
        </label>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full bg-neutral-900 text-neutral-100 rounded shadow border border-neutral-800">
          <thead>
            <tr>
              <th className="px-4 py-2 text-left">Email</th>
              <th className="px-4 py-2">User Agent</th>
              <th className="px-4 py-2">IP Address</th>
              <th className="px-4 py-2">Location</th>
              <th className="px-4 py-2">First Seen</th>
              <th className="px-4 py-2">Last Seen</th>
              <th className="px-4 py-2">New Device</th>
              <th className="px-4 py-2">Details</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr><td colSpan={8} className="text-center py-8">Loading...</td></tr>
            ) : error ? (
              <tr><td colSpan={8} className="text-center text-yellow-500">Failed to load devices from server. Showing mock data.</td></tr>
            ) : paginated.length === 0 ? (
              <tr><td colSpan={8} className="text-center py-8">No devices found</td></tr>
            ) : paginated.map((log: any) => (
              <>
                <tr key={log.id} className="border-b">
                  <td className="px-4 py-2 font-mono text-xs">{log.email}</td>
                  <td className="px-4 py-2 truncate max-w-xs" title={log.user_agent}>{log.user_agent}</td>
                  <td className="px-4 py-2">{log.ip_address}</td>
                  <td className="px-4 py-2">{getLocation(log)}</td>
                  <td className="px-4 py-2 font-mono text-xs">{log.first_seen}</td>
                  <td className="px-4 py-2 font-mono text-xs">{log.last_seen}</td>
                  <td className="px-4 py-2 text-center">{log.first_seen === log.last_seen ? <span className="text-green-600 font-bold">New</span> : ''}</td>
                  <td className="px-4 py-2">
                    <button
                      className="text-blue-600 underline text-xs"
                      onClick={() => setExpanded(expanded === log.id ? null : log.id)}
                    >
                      {expanded === log.id ? 'Hide' : 'View'}
                    </button>
                  </td>
                </tr>
                {expanded === log.id && (
                  <tr>
                    <td colSpan={8} className="bg-gray-50 px-4 py-2">
                      <pre className="text-xs overflow-x-auto">{JSON.stringify(log, null, 2)}</pre>
                    </td>
                  </tr>
                )}
              </>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-4">
        <div>Page {page} of {totalPages || 1}</div>
        <div className="space-x-2">
          <button
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
            className="px-2 py-1 border rounded disabled:opacity-50"
          >Prev</button>
          <button
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
            className="px-2 py-1 border rounded disabled:opacity-50"
          >Next</button>
        </div>
      </div>
    </ProtectedRoute>
  );
} 