import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { CheckCircle, Clock, AlertTriangle, MessageSquare, User, Calendar } from 'lucide-react';
import { authFetch } from '@/utils/api';

interface ResolutionItem {
  id: string;
  feedback_id: string;
  user_id: string;
  user_name: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'resolved' | 'closed';
  title: string;
  description: string;
  resolution_notes?: string;
  assigned_to?: string;
  created_at: string;
  resolved_at?: string;
  response_time?: number;
}

const FeedbackResolution: React.FC = () => {
  const [resolutions, setResolutions] = useState<ResolutionItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResolution, setSelectedResolution] = useState<ResolutionItem | null>(null);
  const [resolutionNotes, setResolutionNotes] = useState('');

  useEffect(() => {
    fetchResolutions();
  }, []);

  const fetchResolutions = async () => {
    try {
      const response = await authFetch('/api/feedback/resolutions');
      if (response.ok) {
        const data = await response.json();
        setResolutions(data.resolutions || []);
      }
    } catch (error) {
      console.error('Error fetching resolutions:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateResolutionStatus = async (id: string, status: string, notes?: string) => {
    try {
      const response = await authFetch(`/api/feedback/resolutions/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status, resolution_notes: notes })
      });
      
      if (response.ok) {
        fetchResolutions();
        setSelectedResolution(null);
        setResolutionNotes('');
      }
    } catch (error) {
      console.error('Error updating resolution:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredResolutions = resolutions.filter(item => {
    const matchesFilter = filter === 'all' || item.status === filter;
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.user_name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Feedback Resolution</h1>
          <p className="text-muted-foreground">
            Manage and resolve user feedback efficiently
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-sm">
            {resolutions.length} Total
          </Badge>
          <Badge variant="outline" className="text-sm">
            {resolutions.filter(r => r.status === 'pending').length} Pending
          </Badge>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search by title or user..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Select value={filter} onValueChange={setFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="resolved">Resolved</SelectItem>
            <SelectItem value="closed">Closed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Resolution List */}
      <div className="grid gap-4">
        {filteredResolutions.map((resolution) => (
          <Card key={resolution.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <CardTitle className="text-lg">{resolution.title}</CardTitle>
                    <Badge className={getPriorityColor(resolution.priority)}>
                      {resolution.priority}
                    </Badge>
                    <Badge className={getStatusColor(resolution.status)}>
                      {resolution.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  <CardDescription className="flex items-center gap-4 text-sm">
                    <span className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      {resolution.user_name}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(resolution.created_at).toLocaleDateString()}
                    </span>
                    {resolution.response_time && (
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {resolution.response_time}h
                      </span>
                    )}
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedResolution(resolution)}
                >
                  View Details
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">
                {resolution.description.substring(0, 150)}...
              </p>
              {resolution.resolution_notes && (
                <div className="bg-muted p-3 rounded-md">
                  <p className="text-sm font-medium mb-1">Resolution Notes:</p>
                  <p className="text-sm text-muted-foreground">{resolution.resolution_notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Resolution Modal */}
      {selectedResolution && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-background rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Resolution Details</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedResolution(null)}
              >
                ×
              </Button>
            </div>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Title</h3>
                <p className="text-sm text-muted-foreground">{selectedResolution.title}</p>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Description</h3>
                <p className="text-sm text-muted-foreground">{selectedResolution.description}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium mb-2">User</h3>
                  <p className="text-sm text-muted-foreground">{selectedResolution.user_name}</p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Category</h3>
                  <p className="text-sm text-muted-foreground">{selectedResolution.category}</p>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Resolution Notes</h3>
                <Textarea
                  value={resolutionNotes}
                  onChange={(e) => setResolutionNotes(e.target.value)}
                  placeholder="Add resolution notes..."
                  rows={4}
                />
              </div>
              
              <div className="flex gap-2">
                <Button
                  onClick={() => updateResolutionStatus(selectedResolution.id, 'in_progress', resolutionNotes)}
                  disabled={selectedResolution.status === 'in_progress'}
                >
                  Mark In Progress
                </Button>
                <Button
                  variant="outline"
                  onClick={() => updateResolutionStatus(selectedResolution.id, 'resolved', resolutionNotes)}
                  disabled={selectedResolution.status === 'resolved'}
                >
                  Mark Resolved
                </Button>
                <Button
                  variant="outline"
                  onClick={() => updateResolutionStatus(selectedResolution.id, 'closed', resolutionNotes)}
                  disabled={selectedResolution.status === 'closed'}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FeedbackResolution; 