import { useState, useEffect, useRef } from 'react';
import { SpeechRecognitionState } from '@/types';
import { normalizeVoiceText } from '@/utils/helpers';

// Extend Window interface for SpeechRecognition
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export const useSpeechRecognition = () => {
  const [state, setState] = useState<SpeechRecognitionState>({
    isListening: false,
    isSupported: false,
    transcript: '',
  });

  const recognitionRef = useRef<any>(null);
  const finalTranscriptRef = useRef<string>('');

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      setState(prev => ({ ...prev, isSupported: true }));
      
      recognitionRef.current = new SpeechRecognition();
      const recognition = recognitionRef.current;

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      recognition.onstart = () => {
        setState(prev => ({ ...prev, isListening: true, error: undefined }));
      };

      recognition.onresult = (event: any) => {
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscriptRef.current += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        // Apply text normalization to maintain proper spacing
        const normalizedFinal = normalizeVoiceText(finalTranscriptRef.current);
        const normalizedInterim = normalizeVoiceText(interimTranscript);
        const combinedTranscript = normalizedFinal + normalizedInterim;

        setState(prev => ({
          ...prev,
          transcript: combinedTranscript,
        }));
      };

      recognition.onerror = (event: any) => {
        setState(prev => ({
          ...prev,
          isListening: false,
          error: event.error,
        }));
      };

      recognition.onend = () => {
        setState(prev => ({ ...prev, isListening: false }));
      };
    } else {
      setState(prev => ({ ...prev, isSupported: false }));
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
    };
  }, []);

  const startListening = () => {
    if (recognitionRef.current && state.isSupported && !state.isListening) {
      // Reset the recognition object to start fresh
      recognitionRef.current.abort();
      recognitionRef.current = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
      const recognition = recognitionRef.current;
      
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      recognition.onstart = () => {
        finalTranscriptRef.current = '';
        setState(prev => ({ ...prev, isListening: true, error: undefined, transcript: '' }));
      };

      recognition.onresult = (event: any) => {
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscriptRef.current += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        // Apply text normalization to maintain proper spacing
        const normalizedFinal = normalizeVoiceText(finalTranscriptRef.current);
        const normalizedInterim = normalizeVoiceText(interimTranscript);
        const combinedTranscript = normalizedFinal + normalizedInterim;

        setState(prev => ({
          ...prev,
          transcript: combinedTranscript,
        }));
      };

      recognition.onerror = (event: any) => {
        setState(prev => ({
          ...prev,
          isListening: false,
          error: event.error,
        }));
      };

      recognition.onend = () => {
        setState(prev => ({ ...prev, isListening: false }));
      };

      recognition.start();
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && state.isListening) {
      recognitionRef.current.stop();
    }
  };

  const resetTranscript = () => {
    finalTranscriptRef.current = '';
    setState(prev => ({ ...prev, transcript: '', error: undefined }));
  };

  return {
    ...state,
    startListening,
    stopListening,
    resetTranscript,
  };
};
