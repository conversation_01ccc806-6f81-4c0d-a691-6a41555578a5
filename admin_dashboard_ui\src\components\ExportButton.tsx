import React, { useRef } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Download, FileText, FileSpreadsheet, Image } from "lucide-react";

interface ExportButtonProps {
  headers?: string[];
  rows?: (string | number)[][];
  exportRef?: React.RefObject<HTMLElement>;
  csvFileName?: string;
  pdfFileName?: string;
  pngFileName?: string;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "default" | "lg";
  className?: string;
}

export const ExportButton: React.FC<ExportButtonProps> = ({
  headers = [],
  rows = [],
  exportRef,
  csvFileName = "export.csv",
  pdfFileName = "export.pdf",
  pngFileName = "export.png",
  variant = "outline",
  size = "sm",
  className = "",
}) => {
  // CSV Export
  const handleExportCSV = () => {
    if (!headers.length && !rows.length) return;
    const csvRows = [];
    if (headers.length) csvRows.push(headers.map(h => `"${h.replace(/"/g, '""')}"`).join(","));
    for (const row of rows) {
      csvRows.push(row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(","));
    }
    const csvContent = "data:text/csv;charset=utf-8," + csvRows.join("\r\n");
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", csvFileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // PDF Export (lazy-load html2pdf.js)
  const handleExportPDF = async () => {
    if (!exportRef?.current) return;
    const html2pdf = (await import(/* webpackChunkName: "html2pdf" */ "html2pdf.js")).default;
    html2pdf(exportRef.current, {
      margin: 0.5,
      filename: pdfFileName,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 2 },
      jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' },
    });
  };

  // PNG Export (lazy-load html2canvas)
  const handleExportPNG = async () => {
    if (!exportRef?.current) return;
    const html2canvas = (await import(/* webpackChunkName: "html2canvas" */ "html2canvas")).default;
    const canvas = await html2canvas(exportRef.current, { scale: 2 });
    canvas.toBlob(blob => {
      if (!blob) return;
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = pngFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, "image/png");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleExportPDF}>
          <FileText className="mr-2 h-4 w-4" />
          Export as PDF
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleExportCSV}>
          <FileSpreadsheet className="mr-2 h-4 w-4" />
          Export as CSV
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleExportPNG}>
          <Image className="mr-2 h-4 w-4" />
          Export as PNG
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}; 