from .admin_auth_api import AdminUserModel

def add_admin_user():
    email = "<EMAIL>"  # Case sensitive admin email
    full_name = "Admin User"  # Default name
    admin_model = AdminUserModel()
    result = admin_model.create_admin(email=email, full_name=full_name)
    if result and result.get('success'):
        print(f"Admin user {email} added with ID {result.get('admin_id')}")
    else:
        print(f"Failed to add admin user {email} (may already exist)")

if __name__ == "__main__":
    add_admin_user() 