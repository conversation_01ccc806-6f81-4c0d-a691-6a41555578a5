import React from 'react';
import { Button } from './ui/button';

const shareUrl = encodeURIComponent(window.location.href);
const shareText = encodeURIComponent('Check this out!');

const shareOptions = [
  {
    name: 'Slack',
    url: `https://slack.com/app_redirect?channel=general`,
    icon: 'https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/slack.svg',
  },
  {
    name: 'Outlook',
    url: `mailto:?subject=Shared Link&body=${shareText}%0A${shareUrl}`,
    icon: 'https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/microsoftoutlook.svg',
  },
  {
    name: 'Mail',
    url: `mailto:?subject=Shared Link&body=${shareText}%0A${shareUrl}`,
    icon: 'https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/maildotru.svg',
  },
  {
    name: 'LinkedIn',
    url: `https://www.linkedin.com/sharing/share-offsite/?url=${shareUrl}`,
    icon: 'https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/linkedin.svg',
  },
  {
    name: 'WhatsApp',
    url: `https://wa.me/?text=${shareText}%20${shareUrl}`,
    icon: 'https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/whatsapp.svg',
  },
  {
    name: 'Telegram',
    url: `https://t.me/share/url?url=${shareUrl}&text=${shareText}`,
    icon: 'https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/telegram.svg',
  },
  // Add more as needed
];

const ShareMenu: React.FC = () => {
  return (
    <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
      {shareOptions.map(option => (
        <Button
          key={option.name}
          asChild
          variant="outline"
          style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}
        >
          <a href={option.url} target="_blank" rel="noopener noreferrer">
            <img src={option.icon} alt={option.name} width={20} height={20} style={{ verticalAlign: 'middle' }} />
            {option.name}
          </a>
        </Button>
      ))}
    </div>
  );
};

export default ShareMenu; 