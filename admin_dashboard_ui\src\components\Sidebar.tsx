import React from "react";
import { NavLink } from "react-router-dom";
import { motion } from "framer-motion";
import { useSidebarStore } from "../hooks/useSidebarStore";
import { useAuthStore } from "../hooks/useAuthStore";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, ChevronLeft, ChevronRight, LayoutDashboard } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useContext } from "react";
import { PermissionsContext } from "../context/PermissionsProvider";

// Icons for each section
import {
  BarChart3,
  MessageSquare,
  Brain,
  Shield,
  Monitor,
  Users,
  Settings,
  TrendingUp,
  AlertTriangle,
  Clock,
  MapPin,
  FileText,
  Trash2,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Mail,
  Zap,
  Target,
  Activity,
  Headphones,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";

const sidebarStructure = [
  {
    label: "Analytics",
    icon: BarChart3,
    value: "analytics",
    children: [
      { 
        label: "Insights",
        path: "/dashboard/analytics/insights",
        roles: ["SUPERADMIN", "VIEWER"],
        icon: BarChart3
      },
    ],
  },
  // Combine Feedback & Escalations
  {
    label: "Feedback & Escalations",
    icon: MessageSquare,
    value: "feedback",
    path: "/dashboard/feedback/trends",
    roles: ["SUPERADMIN", "VIEWER"],
  },
  // Combine AI Insights
  {
    label: "AI Insights",
    icon: Brain,
    value: "ai",
    path: "/dashboard/ai/weekly-digest",
    roles: ["SUPERADMIN", "VIEWER"],
  },
  // Combine Live Support
  {
    label: "Live Support",
    icon: Headphones,
    value: "live",
    path: "/dashboard/live-support/queue",
    roles: ["SUPERADMIN", "VIEWER"],
  },
  // Combine Compliance & Auditing
  {
    label: "Compliance & Auditing",
    icon: Shield,
    value: "compliance",
    path: "/dashboard/compliance/gdpr",
    roles: ["SUPERADMIN"],
  },
  // Leave the rest as is
  {
    label: "Device Intelligence",
    icon: Monitor,
    value: "device-intelligence",
    children: [
      { 
        label: "Device Intelligence",
        path: "/dashboard/device-intelligence",
        roles: ["SUPERADMIN", "VIEWER"],
        icon: Monitor
      },
    ],
  },
  {
    label: "Users",
    icon: Users,
    value: "users",
    children: [
      {
        label: "Admin Users & Roles",
        path: "/dashboard/admin-users-roles",
        roles: ["SUPERADMIN"],
        icon: Users
      },
    ],
  },
  {
    label: "Settings",
    icon: Settings,
    value: "settings",
    children: [
      {
        label: "System Settings",
        path: "/dashboard/system-settings",
        roles: ["SUPERADMIN", "VIEWER"],
        icon: Settings
      },
    ],
  },
];

const SidebarSection = ({ section, role }: any) => {
  const IconComponent = section.icon;
  if (section.path) {
    if (!section.roles.includes(role)) return null;
    return (
      <div className="mb-4">
        <div className="flex items-center gap-2 px-3 py-1 text-xs font-semibold text-muted-foreground uppercase tracking-wide">
          <IconComponent className="h-4 w-4" />
          {section.label}
        </div>
        <div className="pl-7">
          <NavLink
            key={section.path}
            to={section.path}
            className={({ isActive }) =>
              `flex items-center gap-3 px-3 py-2 rounded-lg transition-colors duration-150 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/50 ${
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted hover:text-accent-foreground"
              }`
            }
            tabIndex={0}
            aria-label={section.label}
          >
            <section.icon className="h-4 w-4" />
            <span className="text-sm font-medium">{section.label}</span>
          </NavLink>
        </div>
      </div>
    );
  }
  if (!section.children) return null;
  const filteredChildren = section.children.filter((item: any) => item.roles.includes(role));
  if (filteredChildren.length === 0) return null;
  return (
    <div className="mb-4">
      <div className="flex items-center gap-2 px-3 py-1 text-xs font-semibold text-muted-foreground uppercase tracking-wide">
        <IconComponent className="h-4 w-4" />
        {section.label}
      </div>
      <div className="pl-7">
        {filteredChildren.map((item: any) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center gap-3 px-3 py-2 rounded-lg transition-colors duration-150 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/50 ${
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted hover:text-accent-foreground"
              }`
            }
            tabIndex={0}
            aria-label={item.label}
          >
            <item.icon className="h-4 w-4" />
            <span className="text-sm font-medium">{item.label}</span>
          </NavLink>
        ))}
      </div>
    </div>
  );
};

export const Sidebar = () => {
  const { sidebarOpen, toggleSidebar } = useSidebarStore();
  const permissions = useContext(PermissionsContext);
  const { user, role: authRole } = useAuthStore();
  if (!permissions) throw new Error("PermissionsContext is missing provider!");
  const { role } = permissions;

  // User info display
  const displayName = user?.name || user?.full_name || user?.email || '';
  const displayEmail = user?.email || '';
  const displayRole = user?.role || authRole || '';

  return (
    <aside
      className={`fixed top-0 left-0 h-full bg-background/95 border-r border-border/60 shadow-2xl z-40 flex flex-col dark:bg-zinc-900/95 dark:border-zinc-800/60 transition-all duration-500 ease-out`}
      style={{ width: sidebarOpen ? "260px" : "56px" }}
      tabIndex={-1}
      aria-label="Sidebar navigation"
    >
      {sidebarOpen ? (
        <div>
          <div className="flex items-center justify-between px-5 py-4 border-b border-border/40">
            <img
              src={process.env.PUBLIC_URL + '/favicon.png'}
              alt="Admin Dashboard Logo"
              className="w-10 h-10 rounded-lg shadow-md"
            />
            <button
              onClick={toggleSidebar}
              aria-label={sidebarOpen ? "Collapse sidebar" : "Expand sidebar"}
              className="focus:outline-none hover:bg-muted rounded-lg p-2 transition-colors duration-150 ml-2 border border-border/30 shadow-sm"
              style={{ background: 'none', border: 'none' }}
            >
              <LayoutDashboard className="h-7 w-7 text-blue-600" />
            </button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center pt-6 pb-2">
          <button
            onClick={toggleSidebar}
            aria-label="Expand sidebar"
            className="focus:outline-none"
            style={{ background: 'none', border: 'none', padding: 0, margin: 0 }}
          >
            <img
              src={process.env.PUBLIC_URL + '/favicon.png'}
              alt="Admin Dashboard Logo"
              className="w-10 h-10 rounded-lg shadow-md"
            />
          </button>
        </div>
      )}
      {/* Navigation */}
      {sidebarOpen && (
        <nav className="flex-1 overflow-y-auto py-6 px-2 space-y-2">
          {sidebarStructure.map((section) => (
            <SidebarSection key={section.value} section={section} role={role?.toUpperCase()} />
          ))}
        </nav>
      )}
    </aside>
  );
}; 