import React, { useState, Suspense } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Select } from "@/components/ui/select"; // Assume stub

const FeedbackTrendsChart = React.lazy(() => import("./components/FeedbackTrendsChart"));

const DATE_RANGES = [
  { label: "Last 7 days", value: "7d" },
  { label: "Last 30 days", value: "30d" },
  { label: "Custom", value: "custom" },
];

const FeedbackTrends = () => {
  const [dateRange, setDateRange] = useState("7d");

  return (
    <div className="max-w-3xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle>Feedback Trends</CardTitle>
          <div className="flex flex-wrap gap-2 items-center">
            <Select value={dateRange} onValueChange={setDateRange}>
              {DATE_RANGES.map(opt => (
                <option key={opt.value} value={opt.value}>{opt.label}</option>
              ))}
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-80 w-full" />}>
            <FeedbackTrendsChart dateRange={dateRange} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
};

export default FeedbackTrends; 