import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Paperclip,
  X,
  FileText,
  Search,
  Lightbulb,
  Globe,
  ArrowUp,
  Mic,
  Square
} from 'lucide-react';
import { FileAttachment } from '@/types';
import { cn, formatFileSize } from '@/lib/utils';
import { useHotkeys } from 'react-hotkeys-hook';
import { useSpeechRecognition } from '@/hooks/useSpeechRecognition';

interface ChatInputProps {
  onSendMessage: (message: string, files?: FileAttachment[], responseMode?: string) => void;
  attachedFiles: FileAttachment[];
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  isLoading?: boolean;
  placeholder?: string;
  isEmpty?: boolean; // NEW: true if no messages
  onSummarizeFile?: (file: FileAttachment) => void;
  quotedText?: string; // NEW: quoted text from selection
  onClearQuotedText?: () => void; // NEW: callback to clear quoted text
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  attachedFiles,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  isLoading = false,
  placeholder = "Ask anything about leaves, benefits, or company policies...",
  isEmpty = false,
  onSummarizeFile,
  quotedText,
  onClearQuotedText,
}) => {
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isVoiceRecording, setIsVoiceRecording] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Speech recognition hook
  const {
    isListening,
    isSupported: isSpeechSupported,
    transcript,
    startListening,
    stopListening,
    resetTranscript,
  } = useSpeechRecognition();

  // Keyboard shortcuts
  useHotkeys('cmd+enter,ctrl+enter', () => handleSubmit(), {
    enableOnFormTags: ['textarea'],
  });

  useHotkeys('cmd+k,ctrl+k', (e) => {
    e.preventDefault();
    fileInputRef.current?.click();
  });

  // Voice recording keyboard shortcut
  useHotkeys('cmd+shift+v,ctrl+shift+v', (e) => {
    e.preventDefault();
    handleVoiceToggle();
  });

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto first
      textarea.style.height = 'auto';
      
      // Calculate the proper height based on content
      const computedStyle = window.getComputedStyle(textarea);
      const lineHeight = parseInt(computedStyle.lineHeight) || 24;
      const paddingTop = parseInt(computedStyle.paddingTop) || 0;
      const paddingBottom = parseInt(computedStyle.paddingBottom) || 0;
      
      // Create a temporary element to measure the actual content height
      const temp = document.createElement('div');
      temp.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: ${textarea.offsetWidth}px;
        font-family: ${computedStyle.fontFamily};
        font-size: ${computedStyle.fontSize};
        line-height: ${computedStyle.lineHeight};
        padding: ${computedStyle.padding};
        border: ${computedStyle.border};
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
      `;
      temp.textContent = textarea.value || ' ';
      document.body.appendChild(temp);
      
      const contentHeight = temp.scrollHeight;
      document.body.removeChild(temp);
      
      // Set the height with a maximum limit
      const maxHeight = 200;
      const newHeight = Math.min(contentHeight, maxHeight);
      textarea.style.height = `${newHeight}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  // Update message with transcript when voice recording
  useEffect(() => {
    if (isListening && transcript) {
      setMessage(transcript);
    }
  }, [transcript, isListening]);

  // Handle voice recording state changes
  useEffect(() => {
    setIsVoiceRecording(isListening);
  }, [isListening]);

  const handleVoiceToggle = () => {
    if (!isSpeechSupported) {
      alert('Speech recognition is not supported in your browser.');
      return;
    }

    if (isListening) {
      stopListening();
    } else {
      resetTranscript();
      setMessage('');
      startListening();
    }
  };

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    const trimmed = message.trim();
    
    // Stop voice recording if active
    if (isListening) {
      stopListening();
    }
    
    // If user asks to summarize and exactly one file is attached, call onSummarizeFile
    if (
      trimmed &&
      attachedFiles.length === 1 &&
      /summari[sz]e( this)? file/i.test(trimmed) &&
      typeof onSummarizeFile === 'function'
    ) {
      onSummarizeFile(attachedFiles[0]);
      setMessage('');
      if (textareaRef.current) textareaRef.current.style.height = 'auto';
      return;
    }
    // Otherwise, normal send
    if (trimmed && !isLoading) {
      onSendMessage(trimmed, attachedFiles);
      setMessage('');
      if (textareaRef.current) textareaRef.current.style.height = 'auto';
      
      // Clear quoted text after sending
      if (onClearQuotedText) {
        onClearQuotedText();
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    files.forEach((file) => {
      const fileAttachment: FileAttachment = {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        name: file.name,
        size: file.size,
        type: file.type,
        file: file,
      };
      onAddFile(fileAttachment);
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const canSend = message.trim().length > 0 && !isLoading;

  const containerVariants = {
    focused: {
      boxShadow: "0 0 0 2px hsl(var(--ring))",
      transition: { duration: 0.2 }
    },
    unfocused: {
      boxShadow: "0 0 0 0px hsl(var(--ring))",
      transition: { duration: 0.2 }
    }
  };

  // Waveform animation for recording state
  const WaveformAnimation = () => (
    <div className="flex items-center space-x-0.5">
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="w-0.5 bg-white rounded-full"
          animate={{
            height: [4, 12, 4],
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: i * 0.1,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );

  return (
    <div className={cn(
      "w-full flex flex-col items-center",
      isEmpty ? "max-w-3xl mx-auto" : "max-w-2xl mx-auto pb-6"
    )}>
      {/* File Attachments */}
      <AnimatePresence>
        {attachedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-3 flex flex-wrap gap-2 w-full"
          >
            {attachedFiles.map((file) => (
              <motion.div
                key={file.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 rounded-lg bg-gray-100 dark:bg-gray-700 px-3 py-2 text-sm"
              >
                <FileText className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                <span className="font-medium text-gray-900 dark:text-gray-100">{file.name}</span>
                <span className="text-gray-500 dark:text-gray-400">
                  ({formatFileSize(file.size)})
                </span>
                <button
                  onClick={() => onRemoveFile(file.id)}
                  className="h-5 w-5 rounded hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-400 transition-colors flex items-center justify-center"
                >
                  <X className="h-3 w-3" />
                </button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quoted Text Display */}
      <AnimatePresence>
        {quotedText && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-3 p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                  🧩 Regarding:
                </div>
                <div className="text-sm text-gray-700 dark:text-gray-300 italic">
                  "{quotedText}"
                </div>
              </div>
              <button
                type="button"
                onClick={onClearQuotedText}
                className="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                title="Remove quote"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Input Layout */}
      <motion.form
        onSubmit={handleSubmit}
        variants={containerVariants}
        animate={isFocused ? "focused" : "unfocused"}
        className={cn(
          "relative w-full bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700",
          "shadow-sm hover:shadow-md transition-all duration-200",
          "focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20",
          isVoiceRecording && "ring-2 ring-purple-400/20 border-purple-400"
        )}
      >
        <div className="flex items-center px-4 py-3">
          {/* Left Icons */}
          <div className="flex items-center gap-2 mr-3">
            <button
              type="button"
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Search"
            >
              <Search className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </button>
            <button
              type="button"
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Random suggestion"
            >
              <div className="w-4 h-4 text-gray-500 dark:text-gray-400">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M3 12h18m-9-9l9 9-9 9"/>
                </svg>
              </div>
            </button>
            <button
              type="button"
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Suggestions"
            >
              <Lightbulb className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </button>
          </div>

          {/* Text Input */}
          <div className="flex-1 min-w-0">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              onCompositionStart={() => setIsComposing(true)}
              onCompositionEnd={() => setIsComposing(false)}
              placeholder={isVoiceRecording ? "Listening..." : (isEmpty ? "Ask anything or @mention a Space" : placeholder)}
              className={cn(
                "w-full bg-transparent border-none outline-none resize-none",
                "text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",
                "text-sm leading-5 max-h-32 overflow-y-auto min-h-[10.7px] py-[0.3rem]",
                isVoiceRecording && "placeholder-purple-400"
              )}
              rows={1}
              disabled={isLoading}
            />
          </div>

          {/* Right Icons */}
          <div className="flex items-center gap-2 ml-3">
            <button
              type="button"
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Language"
            >
              <Globe className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </button>
            
            {/* Voice Button with Animation */}
            <motion.button
              type="button"
              onClick={handleVoiceToggle}
              className={cn(
                "p-1.5 rounded-lg transition-all duration-200 relative",
                isVoiceRecording 
                  ? "bg-purple-400 hover:bg-purple-500 text-white" 
                  : "hover:bg-gray-100 dark:hover:bg-gray-700"
              )}
              title={isVoiceRecording ? "Stop recording (Ctrl+Shift+V)" : "Voice input (Ctrl+Shift+V)"}
            >
              <AnimatePresence mode="wait">
                {isVoiceRecording ? (
                  <motion.div
                    key="stop"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="flex items-center justify-center"
                  >
                    <WaveformAnimation />
                  </motion.div>
                ) : (
                  <motion.div
                    key="mic"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="flex items-center justify-center"
                  >
                    <svg 
                      className="w-4 h-4 text-gray-500 dark:text-gray-400" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    >
                      {/* Classic microphone design */}
                      <path d="M12 1a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3Z"/>
                      <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                      <line x1="12" y1="19" x2="12" y2="23"/>
                      <line x1="8" y1="23" x2="16" y2="23"/>
                    </svg>
                  </motion.div>
                )}
              </AnimatePresence>
              

            </motion.button>

            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Attach file"
            >
              <Paperclip className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </button>

            {/* Send Button */}
            <button
              type="submit"
              disabled={!canSend || isVoiceRecording}
              className={cn(
                "p-1.5 rounded-lg transition-all duration-200",
                canSend && !isVoiceRecording
                  ? "bg-black hover:bg-black text-white shadow-sm"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed"
              )}
              style={canSend && !isVoiceRecording ? { backgroundColor: 'black', color: 'white', borderColor: 'black' } : {}}
              title={isVoiceRecording ? "Stop recording first" : "Send message"}
            >
              {isLoading ? (
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <ArrowUp className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.txt,.md,.csv,.xls,.xlsx,.ppt,.pptx,.html,.htm,.json,.xml,.rtf,.odt,.ods,.odp,.png,.jpg,.jpeg,.gif,.bmp,.tiff,.webp"
          onChange={handleFileUpload}
          className="hidden"
        />
      </motion.form>
    </div>
  );
};

export default ChatInput;
