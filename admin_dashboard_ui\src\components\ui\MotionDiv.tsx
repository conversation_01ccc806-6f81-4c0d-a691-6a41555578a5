import { m, HTMLMotionProps } from 'framer-motion';
import { cn } from '../../lib/utils';

// Temporary workaround: use 'any' for props to bypass strict typing issues
const MotionDiv = ({ children, className, ...motionProps }: any) => {
  return (
    <m.div 
      className={cn(className)} 
      style={{ 
        transform: 'translateZ(0)',
        willChange: 'transform',
        backfaceVisibility: 'hidden'
      }}
      {...motionProps}
    >
      {children}
    </m.div>
  );
};

export default MotionDiv; 