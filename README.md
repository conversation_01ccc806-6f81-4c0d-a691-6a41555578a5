# Multi-Model RAG HR Chatbot Platform

A comprehensive HR assistant platform combining advanced RAG (Retrieval-Augmented Generation) with modern admin dashboards, voice interaction, and intelligent document processing.

## 🚀 Core Features

- **Smart RAG Chatbot**: Answers HR questions using company documents and Groq Llama 3
- **Multi-Format Document Processing**: PDF, DOCX, TXT, MD, CSV, XLS, PPTX, HTML, images (OCR)
- **Voice Interface**: Speech-to-text and text-to-speech capabilities
- **Admin Dashboard**: React-based monitoring and management interface
- **Manual Overrides**: Custom response management for specific patterns
- **Email Escalation**: Unanswered queries escalated to HR
- **Authentication**: JWT-based auth with 2FA support
- **Vector Search**: Fast retrieval using Qdrant vector database

## 📁 Project Structure & Components

### 🎯 Main Application Files

- **`app.py`** - Flask backend server with all API endpoints
- **`requirements.txt`** - Python dependencies for backend
- **`config.py`** - Application configuration and settings

### 🏗️ Backend Architecture

#### Core Modules (`src/`)
- **`src/core/`** - Core application resources and utilities
- **`src/chain/`** - RAG chain builder and prompt templates
- **`src/conversation/`** - Chat history management and language detection
- **`src/security/`** - Rate limiting and security features
- **`src/monitoring/`** - Application performance monitoring (APM)

#### Document Processing (`src/document_processing/`)
- **`file_processor.py`** - Multi-format document processing
- **`embedding_generator.py`** - Vector embeddings generation
- **`text_chunker.py`** - Document chunking strategies
- **`ocr_processor.py`** - Image OCR processing
- **`payroll_processor.py`** - Specialized payroll document handling
- **`payroll_classifier.py`** - Payroll document classification
- **`issue_detector.py`** - Document issue detection
- **`training_pipeline.py`** - Model training pipeline
- **`version_control.py`** - Document version management

#### Chunking Strategies (`src/chunking_strategies/`)
- **`base.py`** - Base chunking interface
- **`hr.py`** - HR-specific chunking
- **`recursive.py`** - Recursive document chunking
- **`semantic.py`** - Semantic chunking
- **`token_aware.py`** - Token-aware chunking

#### Retrieval System (`src/retrieval/`)
- **`vector_search.py`** - Qdrant vector search implementation
- **`context_builder.py`** - Context assembly for responses
- **`context_filter.py`** - Context filtering and relevance

#### Intent & NER (`src/intent/` & `src/ner/`)
- **`intent_classifier.py`** - User intent classification
- **`intent_encoder.py`** - Intent encoding utilities
- **`train_classifier.py`** - Intent classifier training
- **`evaluate_classifier.py`** - Model evaluation
- **`data_augmentation.py`** - Training data augmentation
- **`config.yaml`** - Intent classification configuration
- **`entity_extractor.py`** - Named Entity Recognition for HR entities

#### Speech Processing (`src/speech/`)
- **`speech_to_text.py`** - Google Speech Recognition integration
- **`text_to_speech.py`** - MeloTTS text-to-speech

#### User Management (`src/user_authentication/`)
- **`user_authorisation.py`** - User authentication and authorization

#### Database Layer (`src/database/`)
- **`conversation_store.py`** - Chat conversation storage
- **`user_db.py`** - User data management
- **`session_db.py`** - Session management
- **`vector_store.py`** - Vector database operations
- **`payroll_db.py`** - Payroll-specific database operations

#### Utilities (`src/utils/`)
- **`api_status.py`** - API health monitoring
- **`email_service.py`** - Email notification system
- **`error_handler.py`** - Error handling utilities
- **`jwt_utils.py`** - JWT token management
- **`logger.py`** - Structured logging
- **`index_documents.py`** - Document indexing utilities

#### Admin Backend (`admin_dashboard/`)
- **`admin.py`** - Main admin functionality
- **`backend/api/`** - Admin-specific APIs
  - `admin_auth_api.py` - Admin authentication
  - `add_admin_user.py` - User management
  - `update_admin_user.py` - User updates
  - `twofa_manager.py` - 2FA management
  - `override_manager.py` - Manual override management
- **`backend/escalation/`** - Email escalation system
  - `live_escalation.py` - Real-time escalation handling
- **`backend/utils/`** - Admin utilities
  - `email_service.py` - Admin email services
  - `ip_geo.py` - IP geolocation

### 🎨 Frontend Applications

#### Main Chat Interface (`react-frontend/`)
- **`src/App.tsx`** - Main application component
- **`src/components/`** - UI components
  - `chat/` - Chat interface components
    - `ChatContainer.tsx` - Main chat container
    - `ChatInput.tsx` - Input handling
    - `ChatMessages.tsx` - Message display
    - `MessageBubble.tsx` - Individual message bubbles
    - `TypingIndicator.tsx` - Loading indicators
    - `SuggestionChips.tsx` - Quick response suggestions
    - `EscalationList.tsx` - Escalation management
  - `auth/` - Authentication components
    - `PreLoginUI.tsx` - Pre-login interface
  - `layout/` - Layout components
    - `Header.tsx` - Application header
    - `Sidebar.tsx` - Navigation sidebar
  - `modals/` - Modal dialogs
    - `LoginModal.tsx` - Login dialog
    - `RegisterModal.tsx` - Registration dialog
    - `SettingsModal.tsx` - Settings dialog
    - `TwoFAModal.tsx` - 2FA verification
    - `EscalationModal.tsx` - Escalation dialog
    - `FilePreviewModal.tsx` - File preview
    - `ChatSearchModal.tsx` - Chat search
    - `ArchivedChatsModal.tsx` - Archived chats
  - `ui/` - Reusable UI components
    - `button.tsx` - Button components
    - `card.tsx` - Card layouts
    - `input.tsx` - Input fields
    - `dropdown-menu.tsx` - Dropdown menus
    - `FloatingActionButton.tsx` - FAB component
    - `CommandPalette.tsx` - Command interface
- **`src/hooks/`** - Custom React hooks
  - `useAuth.ts` - Authentication hook
  - `useChat.ts` - Chat functionality hook
  - `useSpeechRecognition.ts` - Speech recognition hook
  - `useTheme.ts` - Theme management hook
- **`src/services/`** - API services
  - `api.js` - Backend API integration
- **`src/utils/`** - Utility functions
  - `api.ts` - API utilities
  - `helpers.ts` - Helper functions
  - `storage.ts` - Local storage management
  - `textSelection.ts` - Text selection utilities

#### Admin Dashboard (`admin_dashboard_ui/`)
- **`src/App.tsx`** - Admin dashboard main component
- **`src/components/`** - Dashboard components
  - `AdminDashboard.tsx` - Main dashboard
  - `DashboardCard.tsx` - Dashboard cards
  - `Header.tsx` - Dashboard header
  - `Sidebar.tsx` - Navigation sidebar
  - `Topbar.tsx` - Top navigation bar
  - `LoginPage.tsx` - Admin login
  - `ProtectedRoute.tsx` - Route protection
  - `UserProfile.tsx` - User profile management
  - `RoleBadge.tsx` - Role display component
  - `LiveIndicator.tsx` - Live status indicator
  - `GlobalDateFilter.tsx` - Date filtering
  - `ExportButton.tsx` - Data export functionality
  - `UploadCard.tsx` - File upload interface
  - `ChatLogsCard.tsx` - Chat logs display
  - `QueriesCard.tsx` - Query management
  - `OverridesCard.tsx` - Override management
  - `PayrollManager.tsx` - Payroll management
  - `modals/` - Modal dialogs
    - `AddUserModal.tsx` - Add user dialog
    - `EditRoleModal.tsx` - Role editing
    - `DeleteUserDialog.tsx` - User deletion
  - `ui/` - UI components
    - `LoadingCard.tsx` - Loading states
    - `LoadingSkeleton.tsx` - Skeleton loading
    - `MotionDiv.tsx` - Animated components
    - Various shadcn/ui components (accordion, badge, button, etc.)
- **`src/pages/`** - Dashboard pages
  - `dashboard/` - Main dashboard pages
    - `index.tsx` - Dashboard home
    - `analytics/` - Analytics pages
      - `Overview.tsx` - Analytics overview
      - `Insights.tsx` - Data insights
      - `ChatLogs.tsx` - Chat log analysis
      - `components/` - Analytics components
        - `ChatsPerDayChart.tsx` - Daily chat chart
        - `SentimentPieChart.tsx` - Sentiment analysis
        - `TopicTrendsChart.tsx` - Topic trends
        - `TopIntentsChart.tsx` - Intent analysis
        - `TopQuestionsTable.tsx` - FAQ analysis
    - `live/` - Live monitoring
      - `Ongoing.tsx` - Active conversations
      - `Queue.tsx` - Escalation queue
    - `users/` - User management
      - `Admins.tsx` - Admin users
      - `Roles.tsx` - Role management
    - `training/` - Model training
      - `Misunderstood.tsx` - Misunderstood queries
      - `NERIntent.tsx` - NER and intent training
    - `feedback/` - Feedback management
      - `Trends.tsx` - Feedback trends
      - `Escalated.tsx` - Escalated issues
      - `Resolution.tsx` - Issue resolution
      - `components/` - Feedback components
        - `FeedbackTrendsChart.tsx` - Feedback charts
    - `compliance/` - Compliance management
      - `GDPR.tsx` - GDPR compliance
      - `Sensitive.tsx` - Sensitive data handling
      - `Deletion.tsx` - Data deletion
    - `devices/` - Device management
      - `devices.tsx` - Device overview
      - `DeviceIntelligence.tsx` - Device analytics
    - `settings/` - System settings
      - `SystemSettings.tsx` - System configuration
    - `ai/` - AI features
      - `PolicyDrift.tsx` - Policy drift detection
      - `WeeklyDigest.tsx` - Weekly summaries
    - `audit.tsx` - Audit logs
  - `login/` - Authentication pages
    - `index.tsx` - Login page
    - `verify.tsx` - 2FA verification
  - `register/` - Registration pages
    - `index.tsx` - Registration page
- **`src/hooks/`** - Custom hooks
  - `useAuth.ts` - Authentication hook
  - `useAuthStore.ts` - Auth state management
  - `usePermissions.ts` - Permission management
  - `useQueries.ts` - Query management
  - `useSidebarStore.ts` - Sidebar state
  - `useThemeStore.ts` - Theme management
- **`src/context/`** - React context
  - `PermissionsProvider.tsx` - Permission context
- **`src/providers/`** - Data providers
  - `QueryProvider.tsx` - Query data provider
- **`src/services/`** - API services
  - `api.ts` - Backend API integration
- **`src/types/`** - TypeScript type definitions
  - `index.ts` - Type definitions
- **`src/i18n/`** - Internationalization
  - `index.ts` - i18n configuration
  - `locales/` - Language files (en, es, fr, de, ar, zh)
- **`src/lib/`** - Utility libraries
  - `utils.ts` - Utility functions

### 🗄️ Data Storage

- **`data/`** - All application data
  - `models/` - Trained ML models (NER, intent classifier)
  - `processed/` - Processed documents and embeddings
  - `db/` - SQLite databases
  - `raw/` - Raw uploaded files
  - `raw_files/` - Original document files
  - `models_cache/` - Model caching

### 🐳 Docker Support

- **`docker_files/`** - Docker configuration
  - `Dockerfile` - Application container
  - `docker-compose.yml` - Multi-service orchestration

### 📊 Configuration Files

- **`package.json`** - Node.js dependencies (both frontends)
- **`tailwind.config.js`** - Tailwind CSS configuration
- **`tsconfig.json`** - TypeScript configuration
- **`vite.config.ts`** - Vite build configuration
- **`craco.config.js`** - CRACO configuration (admin dashboard)

## 🔧 API Endpoints

### Core Chat APIs
- `POST /api/query` - Chat with the bot
- `POST /api/upload` - Upload documents
- `POST /api/speech-to-text` - Speech recognition
- `GET /api/file-preview` - Preview uploaded files

### Admin APIs
- `GET /api/queries` - Recent queries
- `GET /api/chatlogs` - Chat logs
- `GET /api/overrides` - Manual overrides
- `POST /api/submit-escalation` - Escalate to HR

### Authentication APIs
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/logout` - User logout
- `GET /api/user` - Current user info

### Analytics APIs
- `GET /api/chat-analytics/live` - Real-time analytics
- `GET /api/health/documents` - Document health check

## 🚀 Quick Start

### Backend Setup
```bash
# Create virtual environment
conda create -p venv python=3.10 -y
conda activate venv/

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys

# Start backend
python app.py
```

### Frontend Setup
```bash
# Main chat interface
cd react-frontend/
npm install
npm run dev

# Admin dashboard
cd admin_dashboard_ui/
npm install
npm start
```

## 🔑 Environment Variables

```env
GROQ_API_KEY=your_groq_api_key
QDRANT_API_KEY=your_qdrant_key
QDRANT_URL=your_qdrant_url
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SENDER_EMAIL=<EMAIL>
HR_EMAILS=<EMAIL>,<EMAIL>
ENABLE_EMAIL_ESCALATION=true
```

## 🛠️ Technologies Used

- **Backend**: Flask, Python 3.10, SQLite, LangChain
- **Frontend**: React 18, TypeScript, Tailwind CSS, Framer Motion
- **AI/ML**: Groq Llama 3, SentenceTransformers, spaCy, PyTorch
- **Vector DB**: Qdrant Cloud
- **Speech**: Google Speech Recognition, MeloTTS
- **Build Tools**: Vite, CRACO, PostCSS
- **UI Components**: shadcn/ui, Radix UI

## 📈 Features Overview

- **Smart Document Processing**: Multi-format support with OCR
- **Intelligent RAG**: Context-aware responses with source attribution
- **Voice Interface**: Natural speech interaction
- **Admin Dashboard**: Comprehensive monitoring and management
- **Manual Overrides**: Custom response management
- **Email Escalation**: HR escalation for complex queries
- **Analytics**: Real-time insights and reporting
- **Security**: JWT authentication with 2FA
- **Compliance**: GDPR and data privacy features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
