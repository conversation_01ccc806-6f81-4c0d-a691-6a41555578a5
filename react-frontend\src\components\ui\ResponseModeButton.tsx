import React from 'react';
import { ChevronDown } from 'lucide-react';

interface ResponseModeButtonProps {
  responseMode: string;
  onResponseModeChange: (mode: string) => void;
  className?: string;
}

const ResponseModeButton: React.FC<ResponseModeButtonProps> = ({
  responseMode,
  onResponseModeChange,
  className = ''
}) => {
  const modes = [
    { value: 'detailed', label: 'Detailed' },
    { value: 'concise', label: 'Concise' },
    { value: 'quick', label: 'Quick' }
  ];

  return (
    <div className={`relative inline-block ${className}`}>
      <select
        value={responseMode}
        onChange={(e) => onResponseModeChange(e.target.value)}
        className="appearance-none bg-white border border-gray-300 rounded-lg px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        {modes.map((mode) => (
          <option key={mode.value} value={mode.value}>
            {mode.label}
          </option>
        ))}
      </select>
      <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
    </div>
  );
};

export default ResponseModeButton;
