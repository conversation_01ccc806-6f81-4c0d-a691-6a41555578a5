import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Menu,
  Plus,
  Search,
  Users,
  MessageSquare,
  MoreVertical
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from '../ui/dropdown-menu';
import { ChatSession } from '@/types';
import { cn } from '@/lib/utils';
import Logo from '/img/favicon.png';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  chatSessions: ChatSession[];
  currentSessionId: string | null;
  onNewChat: () => void;
  onLoadSession: (sessionId: string) => void;
  onDeleteSession: (sessionId: string) => void;
  onArchiveSession: (sessionId: string) => void;
  onRenameSession: (sessionId: string, newTitle: string) => void;
  onDownloadSession: (sessionId: string) => void;
  onOpenSearch: () => void;
  onShowHRTeam: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  onToggle,
  chatSessions,
  currentSessionId,
  onNewChat,
  onLoadSession,
  onDeleteSession,
  onArchiveSession,
  onRenameSession,
  onDownloadSession,
  onOpenSearch,
  onShowHRTeam,
}) => {
  const [renamingSessionId, setRenamingSessionId] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState<string>('');
  const [openMenuSessionId, setOpenMenuSessionId] = useState<string | null>(null);

  const handleRename = (sessionId: string, currentTitle: string) => {
    setRenamingSessionId(sessionId);
    setRenameValue(currentTitle);
  };

  const handleRenameSubmit = (sessionId: string) => {
    if (renameValue.trim()) {
      onRenameSession(sessionId, renameValue.trim());
    }
    setRenamingSessionId(null);
    setRenameValue('');
  };

  const sidebarVariants = {
    expanded: {
      width: 260, // 2.4cm (5cm less than 7.4cm)
      transition: { duration: 0.3, ease: "easeInOut" }
    },
    collapsed: {
      width: 60,
      transition: { duration: 0.3, ease: "easeInOut" }
    }
  };

  return (
    <motion.div
      className={cn(
        "flex flex-col bg-gray-50 border-r border-gray-200 h-full overflow-hidden text-[0.9rem]",
        "dark:bg-gray-900 dark:border-gray-700"
      )}
      variants={sidebarVariants}
      animate={isCollapsed ? "collapsed" : "expanded"}
      id="sidebar"
    >
      {/* Header with logo and toggle */}
      <div className={isCollapsed ? "flex items-center justify-center h-16" : "flex items-center justify-between pl-2 pr-3 py-3"}>
        {isCollapsed ? (
          <button
            onClick={onToggle}
            className="flex items-center justify-center w-12 h-12 rounded-lg focus:outline-none"
            aria-label="Expand sidebar"
          >
            <img src={Logo} alt="Company Logo" className="w-8 h-8 rounded-lg" />
          </button>
        ) : (
          <>
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="flex items-center gap-2"
              >
                <img src={Logo} alt="Company Logo" className="w-8 h-8 rounded-lg" />
              </motion.div>
            </AnimatePresence>
            <button
              onClick={onToggle}
              className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              aria-label="Collapse sidebar"
            >
              <Menu className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
          </>
        )}
      </div>

      {/* New Chat Button */}
      <div className="pl-2 pr-3 pt-3 pb-1"> {/* reduced left padding */}
        <button
          onClick={onNewChat}
          className={cn(
            "w-full flex items-center gap-3 px-3 py-2 rounded-lg",
            // Remove default bg, only show on hover/focus
            "bg-transparent hover:bg-gray-200 text-gray-900 transition-colors",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            isCollapsed && "justify-center px-2"
          )}
        >
          <span className="flex items-center justify-center w-6 h-6 rounded-full transition-transform duration-150 shadow-md hover:scale-110 hover:shadow-2xl" style={{ backgroundColor: '#6D5ACF' }}>
            <Plus className="w-4 h-4 text-white" />
          </span>
          <AnimatePresence>
            {!isCollapsed && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "auto" }}
                exit={{ opacity: 0, width: 0 }}
                className="font-medium whitespace-nowrap"
              >
                New Chat
              </motion.span>
            )}
          </AnimatePresence>
        </button>
      </div>

      {/* Navigation Menu */}
      <nav className="pl-2 pr-3 space-y-1"> {/* reduced left padding */}
        {/* Search */}
        <button
          onClick={onOpenSearch}
          className={cn(
            "w-full flex items-center gap-3 px-3 py-2 rounded-lg",
            "text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700",
            "transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500",
            isCollapsed && "justify-center px-2"
          )}
        >
          <Search className="w-5 h-5" />
          <AnimatePresence>
            {!isCollapsed && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "auto" }}
                exit={{ opacity: 0, width: 0 }}
                className="whitespace-nowrap"
              >
                Search chats
              </motion.span>
            )}
          </AnimatePresence>
        </button>

        {/* HR Team */}
        <button
          onClick={onShowHRTeam}
          className={cn(
            "w-full flex items-center gap-3 px-3 py-2 rounded-lg",
            "text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700",
            "transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500",
            isCollapsed && "justify-center px-2"
          )}
        >
          <Users className="w-5 h-5" />
          <AnimatePresence>
            {!isCollapsed && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "auto" }}
                exit={{ opacity: 0, width: 0 }}
                className="whitespace-nowrap"
              >
                HR Team
              </motion.span>
            )}
          </AnimatePresence>
        </button>
      </nav>

      {/* Chat History Section - Flat List, No Date Grouping */}
      {/* <div className="mt-6" />  Remove the fixed 32px gap between HR Team and Chats */}
      <AnimatePresence>
        {!isCollapsed && chatSessions.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="px-3 pb-3 overflow-y-auto"
          >
            <div className="mt-4 mb-3">
              <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider px-3">
                Chats
              </h3>
            </div>
            <div className="space-y-[0.02rem]"> {/* Further reduced gap between chats */}
              {chatSessions.map((session) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="relative group"
                >
                  <div
                    className={cn(
                      "flex items-center w-full gap-2 px-2 py-1 rounded-lg text-left transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-700",
                      currentSessionId === session.id && "bg-gray-200 dark:bg-gray-700"
                    )}
                  >
                    {isCollapsed && <MessageSquare className="w-4 h-4 flex-shrink-0" />}
                    <div className="flex-1 min-w-0">
                      {renamingSessionId === session.id ? (
                        <input
                          className="font-medium truncate bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-full text-sm"
                          value={renameValue}
                          autoFocus
                          onChange={e => setRenameValue(e.target.value)}
                          onBlur={() => handleRenameSubmit(session.id)}
                          onKeyDown={e => {
                            if (e.key === 'Enter') handleRenameSubmit(session.id);
                            if (e.key === 'Escape') setRenamingSessionId(null);
                          }}
                        />
                      ) : (
                        <div
                          className={cn(
                            "font-normal cursor-pointer font-sans font-inter text-sm flex-1 min-w-0 truncate", // Fill width, always truncate at end
                            currentSessionId === session.id && "text-blue-900 dark:text-blue-100"
                          )}
                          style={{ fontFamily: 'Inter, Segoe UI, Roboto, Arial, sans-serif' }}
                          onClick={() => onLoadSession(session.id)}
                        >
                          {session.title}
                        </div>
                      )}
                    </div>
                    {/* Three-dots menu for chat actions */}
                    <DropdownMenu
                      onOpenChange={(open) => setOpenMenuSessionId(open ? session.id : (openMenuSessionId === session.id ? null : openMenuSessionId))}
                    >
                      <DropdownMenuTrigger asChild>
                        <button
                          className={cn(
                            "p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity duration-150",
                            openMenuSessionId === session.id && "opacity-100"
                          )}
                          title="Chat actions"
                        >
                          <MoreVertical className="w-5 h-5 text-gray-500" />
                        </button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleRename(session.id, session.title)}>
                          Rename
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onDownloadSession(session.id)}>
                          Download
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onArchiveSession(session.id)}>
                          Archive
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => onDeleteSession(session.id)} className="text-red-600">
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Collapsed Chat History */}
      <AnimatePresence>
        {isCollapsed && chatSessions.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex-1 px-2 pb-3 overflow-y-auto"
          >
            <div className="space-y-1">
              {chatSessions.slice(0, 5).map((session) => (
                <button
                  key={session.id}
                  onClick={() => onLoadSession(session.id)}
                  className={cn(
                    "w-full p-2 rounded-lg transition-colors",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500",
                    currentSessionId === session.id
                      ? "bg-blue-100 dark:bg-blue-900"
                      : "hover:bg-gray-200 dark:hover:bg-gray-700"
                  )}
                  title={session.title}
                >
                  <MessageSquare className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default Sidebar;
