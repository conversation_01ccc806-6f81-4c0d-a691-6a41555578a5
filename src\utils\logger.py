"""
Optimized logging configuration for performance.
Uses non-blocking writes and async handlers where possible.
"""

import logging
import logging.handlers
import sys
import os
from pathlib import Path
from typing import Optional
import threading
import queue
import time

# Global logger cache
_logger_cache = {}

class AsyncLogHandler(logging.Handler):
    """Non-blocking log handler that writes to a queue."""
    
    def __init__(self, filename: str, max_bytes: int = 10*1024*1024, backup_count: int = 5):
        super().__init__()
        self.filename = filename
        self.max_bytes = max_bytes
        self.backup_count = backup_count
        
        # Create rotating file handler with UTF-8 encoding
        self.handler = logging.handlers.RotatingFileHandler(
            filename, maxBytes=max_bytes, backupCount=backup_count, encoding='utf-8'
        )
        self.handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        
        # Start async writer thread
        self.queue = queue.Queue(maxsize=1000)
        self.thread = threading.Thread(target=self._async_writer, daemon=True)
        self.thread.start()
    
    def emit(self, record):
        """Add record to queue for async processing."""
        try:
            self.queue.put_nowait(record)
        except queue.Full:
            # If queue is full, just drop the log message
            pass
    
    def _async_writer(self):
        """Async writer thread."""
        while True:
            try:
                record = self.queue.get(timeout=1)
                self.handler.emit(record)
            except queue.Empty:
                continue
            except Exception:
                # Continue even if there's an error
                continue

class SafeConsoleHandler(logging.StreamHandler):
    """Console handler that safely handles Unicode characters."""
    
    def __init__(self, stream=None):
        super().__init__(stream)
        # Set encoding to UTF-8 for safe Unicode handling
        if hasattr(self.stream, 'reconfigure'):
            try:
                self.stream.reconfigure(encoding='utf-8')
            except:
                pass
    
    def emit(self, record):
        """Emit a record with safe Unicode handling."""
        try:
            msg = self.format(record)
            # Remove or replace problematic Unicode characters
            msg = self._sanitize_message(msg)
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)
    
    def _sanitize_message(self, msg: str) -> str:
        """Remove or replace Unicode characters that cause encoding issues."""
        # Replace common emoji and Unicode characters with text equivalents
        replacements = {
            '✅': '[OK]',
            '❌': '[ERROR]',
            '⚠️': '[WARNING]',
            '🔌': '[CONNECT]',
            '💻': '[CPU]',
            '📂': '[FOLDER]',
            '🔍': '[SEARCH]',
            '🔄': '[REFRESH]',
            '🎯': '[TARGET]',
            '📊': '[CHART]',
            '🚀': '[ROCKET]',
            '⚡': '[FAST]',
            '🔧': '[TOOL]',
            '📝': '[NOTE]',
            '🎉': '[SUCCESS]',
            '💡': '[IDEA]',
            '🔒': '[LOCK]',
            '🔓': '[UNLOCK]',
            '📈': '[UP]',
            '📉': '[DOWN]',
            '🔄': '[SYNC]',
            '⏱️': '[TIME]',
            '📋': '[CLIPBOARD]',
            '🎨': '[DESIGN]',
            '🔍': '[SEARCH]',
            '📱': '[MOBILE]',
            '💾': '[SAVE]',
            '🖥️': '[DESKTOP]',
            '📄': '[DOCUMENT]',
            '📁': '[FOLDER]',
            '🔗': '[LINK]',
            '📌': '[PIN]',
            '🎪': '[CIRCUS]',
            '🎭': '[THEATER]',
            '🎨': '[ART]',
            '🎬': '[MOVIE]',
            '🎵': '[MUSIC]',
            '🎮': '[GAME]',
            '🏆': '[TROPHY]',
            '🏅': '[MEDAL]',
            '🎖️': '[MEDAL]',
            '🏁': '[FINISH]',
            '🚦': '[TRAFFIC]',
            '🚧': '[CONSTRUCTION]',
            '🚨': '[ALERT]',
            '🚩': '[FLAG]',
            '🚪': '[DOOR]',
            '🚿': '[SHOWER]',
            '🛁': '[BATH]',
            '🛏️': '[BED]',
            '🛋️': '[COUCH]',
            '🛎️': '[BELL]',
            '🛒': '[CART]',
            '🛕': '[TEMPLE]',
            '🛖': '[HUT]',
            '🛗': '[ELEVATOR]',
            '🛘': '[BUILDING]',
            '🛙': '[SHOP]',
            '🛚': '[FACTORY]',
            '🛛': '[WAREHOUSE]',
            '🛜': '[WIFI]',
            '🛝': '[SLIDE]',
            '🛞': '[WHEEL]',
            '🛟': '[TOOL]',
            '🛠️': '[TOOLS]',
            '🛡️': '[SHIELD]',
            '🛢️': '[OIL]',
            '🛣️': '[ROAD]',
            '🛤️': '[TRACK]',
            '🛥️': '[BOAT]',
            '🛦': '[PLANE]',
            '🛧': '[PLANE]',
            '🛨': '[HELICOPTER]',
            '🛩': '[PLANE]',
            '🛪': '[PLANE]',
            '🛫': '[PLANE]',
            '🛬': '[PLANE]',
            '🛭': '[SATELLITE]',
            '🛮': '[ROCKET]',
            '🛯': '[ROCKET]',
            '🛰': '[SATELLITE]',
            '🛱': '[CAR]',
            '🛲': '[BIKE]',
            '🛳': '[SHIP]',
            '🛴': '[SCOOTER]',
            '🛵': '[MOPED]',
            '🛶': '[CANOE]',
            '🛷': '[SLED]',
            '🛸': '[UFO]',
            '🛹': '[SKATEBOARD]',
            '🛺': '[AUTO]',
            '🛻': '[TRUCK]',
            '🛼': '[ROLLER]',
            '🛽': '[GAS]',
            '🛾': '[MOTORCYCLE]',
            '🛿': '[SNOWMOBILE]',
        }
        
        for emoji, replacement in replacements.items():
            msg = msg.replace(emoji, replacement)
        
        # Remove any remaining problematic Unicode characters
        try:
            msg.encode('cp1252')
            return msg
        except UnicodeEncodeError:
            # If still problematic, remove all non-ASCII characters
            return ''.join(char for char in msg if ord(char) < 128)

def get_logger(name: str, level: Optional[int] = None) -> logging.Logger:
    """
    Get a logger with optimized configuration.
    
    Args:
        name: Logger name
        level: Optional log level override
        
    Returns:
        Configured logger
    """
    if name in _logger_cache:
        return _logger_cache[name]
    
    logger = logging.getLogger(name)
    
    # Set level
    if level is not None:
        logger.setLevel(level)
    else:
        logger.setLevel(logging.INFO)
    
    # Avoid duplicate handlers
    if logger.handlers:
        _logger_cache[name] = logger
        return logger
    
    # Create logs directory
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Add async file handler
    log_file = logs_dir / f"{name}.log"
    file_handler = AsyncLogHandler(str(log_file))
    file_handler.setLevel(logging.DEBUG)
    logger.addHandler(file_handler)
    
    # Add safe console handler for errors only (to reduce console spam)
    console_handler = SafeConsoleHandler(sys.stderr)
    console_handler.setLevel(logging.WARNING)
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    logger.addHandler(console_handler)
    
    # Disable propagation to avoid duplicate logs
    logger.propagate = False
    
    _logger_cache[name] = logger
    return logger

def configure_root_logger(level: int = logging.INFO):
    """Configure the root logger with optimized settings."""
    # Clear existing handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Set level
    root_logger.setLevel(level)
    
    # Add async file handler for all logs
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    file_handler = AsyncLogHandler(str(logs_dir / "app.log"))
    file_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)
    
    # Add safe console handler for warnings and errors
    console_handler = SafeConsoleHandler(sys.stderr)
    console_handler.setLevel(logging.WARNING)
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    root_logger.addHandler(console_handler)

# Performance monitoring
class PerformanceLogger:
    """Logger for performance metrics."""
    
    def __init__(self, name: str = "performance"):
        self.logger = get_logger(name)
        self.timers = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation."""
        self.timers[operation] = time.time()
    
    def end_timer(self, operation: str, extra_info: str = ""):
        """End timing an operation and log the duration."""
        if operation in self.timers:
            duration = time.time() - self.timers[operation]
            self.logger.info(f"{operation} completed in {duration:.3f}s {extra_info}")
            del self.timers[operation]
    
    def log_metric(self, metric: str, value: float, unit: str = ""):
        """Log a performance metric."""
        self.logger.info(f"METRIC: {metric} = {value:.3f} {unit}")

# Global performance logger
perf_logger = PerformanceLogger()