import numpy as np
import sounddevice as sd
from typing import Dict, Any, Optional
import time
import sys
import speech_recognition as sr
import re
import unicodedata
import os
from pathlib import Path

from ..utils.logger import get_logger
from ..config import SPEECH_RECOGNITION_DURATION, <PERSON>EECH_SAMPLE_RATE, WHISPER_MODEL_NAME, WHISPER_MODEL_DIR

logger = get_logger(__name__)

# Try to import whisper
try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False
    logger.warning("Whisper not available. Install with: pip install openai-whisper")

# --- Custom Exceptions for better error handling ---
class SpeechToTextError(Exception):
    """Base exception for speech-to-text operations."""
    pass

class AudioRecordingError(SpeechToTextError):
    """Raised when there's an issue with audio recording."""
    pass

class ModelLoadingError(SpeechToTextError):
    """Raised when the speech recognition model fails to load."""
    pass

class TranscriptionError(SpeechToTextError):
    """Raised when audio transcription fails."""
    pass

def normalize_voice_text(text: str) -> str:
    """
    Normalizes voice input text to maintain correct single spacing.
    
    Args:
        text: Raw text from voice input
        
    Returns:
        Normalized text with proper spacing
    """
    if not text:
        return ''
    
    # Normalize Unicode characters
    normalized = unicodedata.normalize("NFKC", text)
    
    # Replace various Unicode spaces and control characters
    replacements = {
        "\u00A0": " ",      # Non-breaking space
        "\u2028": "\n",     # Line separator
        "\u2029": "\n\n",   # Paragraph separator
        "\u200B": "",       # Zero-width space
        "\u200C": "",       # Zero-width non-joiner
        "\u200D": "",       # Zero-width joiner
        "\uFEFF": "",       # Byte order mark
        "\u2060": "",       # Word joiner
        "\u00AD": "",       # Soft hyphen
    }
    
    for old, new in replacements.items():
        normalized = normalized.replace(old, new)
    
    # Normalize line endings
    normalized = re.sub(r'\r\n?', '\n', normalized)
    
    # Reduce multiple consecutive newlines to single
    normalized = re.sub(r'\n{3,}', '\n\n', normalized)
    
    # Normalize whitespace - replace multiple spaces/tabs with single space
    normalized = re.sub(r'[ \t]+', ' ', normalized)
    
    # Remove spaces around newlines
    normalized = re.sub(r' *\n *', '\n', normalized)
    
    # Remove leading/trailing whitespace
    normalized = normalized.strip()
    
    return normalized

class SpeechToText:
    """
    Convert speech to text using either Whisper (local) or speech_recognition (Google API).
    Includes features for audio recording, lazy model loading,
    and enhanced error handling for production environments.
    """

    def __init__(self, use_whisper: bool = True):
        """
        Initialize the speech-to-text converter.
        
        Args:
            use_whisper: If True, use local Whisper model. If False, use Google API.
        """
        self.recognizer = sr.Recognizer()
        self._can_record = True
        self.use_whisper = use_whisper and WHISPER_AVAILABLE
        self.whisper_model = None
        
        # Basic check for sounddevice availability - important for record_audio
        try:
            sd.check_input_parameters(dtype=np.float32, channels=1, samplerate=SPEECH_SAMPLE_RATE)
            logger.info("Sounddevice input parameters checked successfully.")
        except sd.PortAudioError as e:
            logger.critical(f"Sounddevice initialization failed. Audio recording may not work: {e}. "
                            "Ensure microphone is connected and drivers are installed.")
            self._can_record = False
        except Exception as e:
            logger.critical(f"Unexpected error during sounddevice check: {e}")
            self._can_record = False

    def _load_whisper_model(self):
        """Load the Whisper model from local cache."""
        if not WHISPER_AVAILABLE:
            raise ModelLoadingError("Whisper is not available. Install with: pip install openai-whisper")
        
        if self.whisper_model is not None:
            return self.whisper_model
        
        try:
            # Check if we have a local model
            local_model_path = Path("data/models_cache/whisper_model")
            if local_model_path.exists() and (local_model_path / "small.pt").exists():
                logger.info("Loading Whisper model from local cache...")
                self.whisper_model = whisper.load_model(str(local_model_path / "small.pt"))
                logger.info("[OK] Whisper model loaded from local cache")
            else:
                logger.info(f"Loading Whisper model '{WHISPER_MODEL_NAME}' from HuggingFace...")
                self.whisper_model = whisper.load_model(WHISPER_MODEL_NAME)
                logger.info("[OK] Whisper model loaded from HuggingFace")
            
            return self.whisper_model
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            raise ModelLoadingError(f"Failed to load Whisper model: {e}")

    def record_audio(self, duration: int = SPEECH_RECOGNITION_DURATION, samplerate: int = SPEECH_SAMPLE_RATE) -> np.ndarray:
        """
        Record audio from the microphone for a specified duration.

        Args:
            duration: Duration of recording in seconds. Must be a positive integer.
            samplerate: Sample rate in Hz. Must be a positive integer.

        Returns:
            NumPy array of audio data (float32).

        Raises:
            AudioRecordingError: If recording fails due to invalid parameters or device issues.
        """
        if not self._can_record:
            raise AudioRecordingError("Audio recording is not available. Microphone might not be configured or sounddevice failed to initialize.")

        if not isinstance(duration, (int, float)) or duration <= 0:
            raise AudioRecordingError(f"Invalid duration: {duration}. Must be a positive number.")
        if not isinstance(samplerate, int) or samplerate <= 0:
            raise AudioRecordingError(f"Invalid samplerate: {samplerate}. Must be a positive integer.")

        # Ensure duration is not excessively long for typical HR queries
        if duration > 60: # Example: Limit to 60 seconds for a single recording
            logger.warning(f"Recording duration ({duration}s) exceeds recommended limit. Truncating to 60s.")
            duration = 60

        try:
            logger.info(f"Recording audio for {duration} seconds at {samplerate} Hz...")

            # Calculate number of frames
            frames_to_record = int(duration * samplerate)
            if frames_to_record <= 0:
                raise AudioRecordingError(f"Calculated frames to record is zero or negative: {frames_to_record}.")

            # Record audio
            recording = sd.rec(
                frames_to_record,
                samplerate=samplerate,
                channels=1, # Mono recording is sufficient
                dtype=np.float32
            )
            sd.wait()  # Wait for recording to complete

            # Remove extra dimension (e.g., from (N, 1) to (N,))
            audio_data = np.squeeze(recording)

            actual_duration = len(audio_data) / samplerate
            logger.info(f"Finished recording. Actual duration: {actual_duration:.2f} seconds.")

            return audio_data

        except sd.PortAudioError as e:
            logger.error(f"PortAudio error during recording: {e}. Check microphone connection and permissions.", exc_info=True)
            raise AudioRecordingError(f"Audio recording device error: {e}") from e
        except Exception as e:
            logger.error(f"Unexpected error during audio recording: {e}", exc_info=True)
            raise AudioRecordingError(f"An unexpected error occurred during recording: {e}") from e

    def transcribe(self, audio_data: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Transcribe audio (either provided or recorded) to text using Whisper or Google API.

        Args:
            audio_data: Pre-recorded audio data as a NumPy array (float32).
                        If None, audio will be recorded from the microphone.

        Returns:
            A dictionary containing the transcription result (e.g., {"text": "...", "language": "...", ...}).
            Includes an "error" key if transcription fails.

        Raises:
            AudioRecordingError: If audio recording is required but fails.
            TranscriptionError: If the speech recognition process itself fails.
        """
        try:
            # Record audio if not provided
            if audio_data is None:
                audio_data = self.record_audio()
                if audio_data is None or audio_data.size == 0:
                    logger.warning("No audio data captured for transcription.")
                    return {"text": "", "error": "No audio data captured."}
            elif not isinstance(audio_data, np.ndarray) or audio_data.dtype != np.float32:
                logger.error(f"Invalid audio_data type or dtype: {type(audio_data)}, {getattr(audio_data, 'dtype', 'N/A')}. Expected numpy.ndarray with dtype float32.")
                raise TranscriptionError("Invalid audio data format provided. Expected NumPy array of float32.")
            elif audio_data.size == 0:
                logger.warning("Provided audio_data is empty.")
                return {"text": "", "error": "Provided audio data is empty."}

            logger.info(f"Starting transcription using {'Whisper' if self.use_whisper else 'Google API'}. Audio length: {len(audio_data) / SPEECH_SAMPLE_RATE:.2f}s")
            start_time = time.perf_counter()

            if self.use_whisper:
                # Use Whisper for transcription
                transcribed_text = self._transcribe_with_whisper(audio_data)
            else:
                # Use Google API for transcription
                transcribed_text = self._transcribe_with_google(audio_data)

            transcription_time = time.perf_counter() - start_time

            # Apply text normalization to maintain proper spacing
            transcribed_text = normalize_voice_text(transcribed_text.strip())
            if not transcribed_text:
                logger.warning("Speech recognition returned an empty transcription.")

            logger.info(f"Transcription complete in {transcription_time:.2f}s. Transcribed: '{transcribed_text[:70]}...'")

            return {"text": transcribed_text}

        except AudioRecordingError as e:
            # Re-raise specific errors directly
            raise e
        except sr.UnknownValueError:
            logger.error("Speech recognition could not understand audio.")
            return {"text": "", "error": "Could not understand audio."}
        except sr.RequestError as e:
            logger.error(f"Could not request results from speech recognition service; {e}")
            return {"text": "", "error": f"Speech recognition service error: {e}"}
        except Exception as e:
            logger.error(f"An unexpected error occurred during transcription: {e}", exc_info=True)
            raise TranscriptionError(f"Transcription failed: {e}") from e

    def _transcribe_with_whisper(self, audio_data: np.ndarray) -> str:
        """Transcribe audio using Whisper model."""
        try:
            # Load Whisper model if not already loaded
            model = self._load_whisper_model()
            
            # Save audio to temporary file (Whisper expects a file path)
            import tempfile
            import soundfile as sf
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                # Convert to int16 for WAV format
                audio_int16 = (audio_data * 32767).astype(np.int16)
                sf.write(temp_file.name, audio_int16, SPEECH_SAMPLE_RATE)
                
                # Transcribe with Whisper
                result = model.transcribe(temp_file.name)
                
                # Clean up temporary file
                os.unlink(temp_file.name)
                
                return result["text"]
        except Exception as e:
            logger.error(f"Whisper transcription failed: {e}")
            raise TranscriptionError(f"Whisper transcription failed: {e}")

    def _transcribe_with_google(self, audio_data: np.ndarray) -> str:
        """Transcribe audio using Google API."""
        try:
            # Convert numpy array to AudioData for speech_recognition
            audio_data_int16 = (audio_data * 32767).astype(np.int16)
            audio_data_bytes = audio_data_int16.tobytes()
            audio = sr.AudioData(audio_data_bytes, SPEECH_SAMPLE_RATE, 2)

            # Transcribe audio using Google API
            result = self.recognizer.recognize_google(audio)
            return result
        except Exception as e:
            logger.error(f"Google API transcription failed: {e}")
            raise TranscriptionError(f"Google API transcription failed: {e}")

    def recognize_speech(self) -> str:
        """
        Initiates microphone recording and transcribes the speech to text.
        A user prompt "Listening... Speak now!" is displayed.

        Returns:
            The transcribed text as a string. Returns an empty string if
            no speech is detected or an error occurs.
        """
        print("Listening... Speak now!")

        try:
            result = self.transcribe() # This will call record_audio if no audio_data is passed

            text = result.get("text", "").strip()

            if text:
                print(f"You said: {text}")
            else:
                # Check for specific error message
                error_message = result.get("error")
                if error_message:
                    print(f"No speech detected or error: {error_message}")
                else:
                    print("No speech detected.")

            return text

        except (AudioRecordingError, TranscriptionError) as e:
            logger.error(f"Error during speech recognition: {e}", exc_info=True)
            print(f"Error recognizing speech: {e}. Please try again.")
            return ""
        except Exception as e:
            logger.error(f"An unexpected error occurred during speech recognition flow: {e}", exc_info=True)
            print("An unexpected error occurred during speech recognition. Please try again.")
            return ""

# Example Usage (for local testing)
if __name__ == "__main__":
    # Mock the get_logger and config imports for standalone testing
    import os
    from pathlib import Path

    class MockLogger:
        def __init__(self, name): self.name = name
        def info(self, msg, **kwargs): print(f"INFO: [{self.name}] {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: [{self.name}] {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: [{self.name}] {msg}")
        def debug(self, msg, **kwargs): pass
        def critical(self, msg, **kwargs): print(f"CRITICAL: [{self.name}] {msg}")

    _original_get_logger = globals().get('get_logger')
    _original_SPEECH_RECOGNITION_DURATION = globals().get('SPEECH_RECOGNITION_DURATION')
    _original_SPEECH_SAMPLE_RATE = globals().get('SPEECH_SAMPLE_RATE')

    globals()['get_logger'] = MockLogger
    globals()['SPEECH_RECOGNITION_DURATION'] = 5 # Set a short duration for testing
    globals()['SPEECH_SAMPLE_RATE'] = 16000 # Standard sample rate

    print("--- Testing SpeechToText Class ---")
    try:
        # Initialize the speech-to-text converter
        stt_converter = SpeechToText()

        print("\nAttempting to recognize speech (will record for 5 seconds)...")
        text_from_mic = stt_converter.recognize_speech()
        print(f"Recognized text from mic: '{text_from_mic}'")

        # Test with a dummy audio array
        print("\nTesting transcription with dummy audio data...")
        # Create a dummy silence audio array (e.g., 2 seconds of silence)
        dummy_audio = np.zeros(SPEECH_SAMPLE_RATE * 2, dtype=np.float32)
        dummy_result = stt_converter.transcribe(audio_data=dummy_audio)
        print(f"Transcription result for dummy audio: {dummy_result.get('text', 'N/A')}")
        if dummy_result.get('error'):
            print(f"  Error: {dummy_result['error']}")

    except Exception as e:
        print(f"An unexpected error occurred during testing: {e}")

    finally:
        # Restore original imports
        if '_original_get_logger' in locals(): globals()['get_logger'] = _original_get_logger
        if '_original_SPEECH_RECOGNITION_DURATION' in locals(): globals()['SPEECH_RECOGNITION_DURATION'] = _original_SPEECH_RECOGNITION_DURATION
        if '_original_SPEECH_SAMPLE_RATE' in locals(): globals()['SPEECH_SAMPLE_RATE'] = _original_SPEECH_SAMPLE_RATE
