import React from "react";

type SelectProps = React.SelectHTMLAttributes<HTMLSelectElement> & {
  onValueChange?: (value: string) => void;
};

export const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  ({ value, onChange, onValueChange, children, ...props }, ref) => (
    <select
      ref={ref}
      value={value}
      onChange={e => {
        onChange?.(e);
        onValueChange?.(e.target.value);
      }}
      {...props}
    >
      {children}
    </select>
  )
);
Select.displayName = 'Select';

type SelectTriggerProps = React.HTMLAttributes<HTMLDivElement> & {
  children: React.ReactNode;
};

export const SelectTrigger = React.forwardRef<HTMLDivElement, SelectTriggerProps>(
  ({ children, ...props }, ref) => (
    <div ref={ref} {...props}>
      {children}
    </div>
  )
);
SelectTrigger.displayName = 'SelectTrigger';

type SelectValueProps = React.HTMLAttributes<HTMLDivElement> & {
  placeholder?: string;
  children?: React.ReactNode;
};

export const SelectValue = React.forwardRef<HTMLDivElement, SelectValueProps>(
  ({ placeholder, children, ...props }, ref) => (
    <div ref={ref} {...props}>
      {children || placeholder}
    </div>
  )
);
SelectValue.displayName = 'SelectValue';

type SelectContentProps = React.HTMLAttributes<HTMLDivElement> & {
  children: React.ReactNode;
};

export const SelectContent = React.forwardRef<HTMLDivElement, SelectContentProps>(
  ({ children, ...props }, ref) => (
    <div ref={ref} {...props}>
      {children}
    </div>
  )
);
SelectContent.displayName = 'SelectContent';

type SelectItemProps = React.HTMLAttributes<HTMLDivElement> & {
  value: string;
  children: React.ReactNode;
};

export const SelectItem = React.forwardRef<HTMLDivElement, SelectItemProps>(
  ({ value, children, ...props }, ref) => (
    <div ref={ref} data-value={value} {...props}>
      {children}
    </div>
  )
);
SelectItem.displayName = 'SelectItem'; 