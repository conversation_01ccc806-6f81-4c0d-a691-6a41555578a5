import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "recharts";

const data = [
  { date: "Mon", Leave: 30, Payslip: 20, Attendance: 10 },
  { date: "Tue", Leave: 40, Payslip: 25, Attendance: 15 },
  { date: "Wed", Leave: 35, Payslip: 30, Attendance: 20 },
  { date: "Thu", Leave: 50, Payslip: 28, Attendance: 18 },
  { date: "Fri", Leave: 60, Payslip: 35, Attendance: 25 },
  { date: "Sat", Leave: 20, Payslip: 15, Attendance: 8 },
  { date: "Sun", Leave: 10, Payslip: 8, Attendance: 5 },
];

const TopicTrendsChart = () => (
  <div className="h-64 w-full">
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data} margin={{ top: 16, right: 16, left: 0, bottom: 0 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#444" />
        <XAxis dataKey="date" stroke="#888" />
        <YAxis stroke="#888" />
        <Tooltip />
        <Legend />
        <Line type="monotone" dataKey="Leave" stroke="#6366f1" strokeWidth={2} />
        <Line type="monotone" dataKey="Payslip" stroke="#10b981" strokeWidth={2} />
        <Line type="monotone" dataKey="Attendance" stroke="#f59e42" strokeWidth={2} />
      </LineChart>
    </ResponsiveContainer>
  </div>
);

export default TopicTrendsChart; 