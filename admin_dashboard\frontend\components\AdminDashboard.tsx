import React, { useState, useEffect } from "react";
import { Sun, Moon, FilePlus, ListChecks, MessageSquareText, Settings2 } from "lucide-react";
import { Link } from "react-router-dom";
import favicon from '../../favicon.png';

// Example actions for the dashboard
const actions = [
  {
    title: "Upload HR Document",
    description: "Add new HR policies, forms, or documents to the system.",
    to: "/upload_doc",
    icon: <FilePlus className="w-7 h-7 text-white group-hover:scale-110 transition-transform" />,
    gradient: "from-indigo-500 to-blue-500",
  },
  {
    title: "View Recent Queries",
    description: "Review the latest employee questions and requests.",
    to: "/queries",
    icon: <ListChecks className="w-7 h-7 text-white group-hover:scale-110 transition-transform" />,
    gradient: "from-emerald-500 to-teal-500",
  },
  {
    title: "View Chat Logs",
    description: "Monitor and audit chat conversations for compliance.",
    to: "/chat_logs",
    icon: <MessageSquareText className="w-7 h-7 text-white group-hover:scale-110 transition-transform" />,
    gradient: "from-blue-600 to-cyan-500",
  },
  {
    title: "Manage Manual Overrides",
    description: "Manually adjust or override chatbot responses.",
    to: "/overrides",
    icon: <Settings2 className="w-7 h-7 text-white group-hover:scale-110 transition-transform" />,
    gradient: "from-fuchsia-500 to-pink-500",
  },
];

const AdminDashboard: React.FC = () => {
  // Dark mode state
  const [darkMode, setDarkMode] = useState(() =>
    window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches
  );

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [darkMode]);

  // Set font globally for this dashboard (Inter or Open Sans)
  useEffect(() => {
    document.body.style.fontFamily =
      'Inter, Open Sans, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif';
    return () => {
      document.body.style.fontFamily = "";
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-slate-200 to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 text-slate-900 dark:text-slate-100 font-sans transition-colors duration-300">
      {/* Navbar */}
      <nav className="w-full bg-slate-900/90 dark:bg-slate-900/95 border-b border-slate-800 shadow-lg">
        <div className="max-w-6xl mx-auto flex items-center justify-between px-6 py-4">
          {/* Logo & Title */}
          <div className="flex items-center gap-3">
            <img
              src={favicon}
              alt="Admin Dashboard Logo"
              className="w-10 h-10 rounded-full shadow-lg object-cover bg-white"
            />
            <span className="ml-2 text-2xl md:text-3xl font-extrabold tracking-tight text-white drop-shadow-lg">
              HR Admin Dashboard
            </span>
          </div>
          {/* Navbar Right: Dark/Light Toggle & User */}
          <div className="flex items-center gap-6">
            <button
              aria-label="Toggle dark mode"
              onClick={() => setDarkMode((d) => !d)}
              className="rounded-full p-2 bg-slate-800 hover:bg-slate-700 transition-colors border border-slate-700 shadow"
            >
              {darkMode ? (
                <Sun className="w-5 h-5 text-yellow-300" />
              ) : (
                <Moon className="w-5 h-5 text-slate-200" />
              )}
            </button>
            <div className="flex items-center gap-2">
              <div className="w-10 h-10 rounded-full bg-gradient-to-tr from-indigo-600 to-blue-600 flex items-center justify-center text-lg font-semibold shadow-md">
                <span>AD</span>
              </div>
              <span className="hidden md:inline text-slate-200 font-medium tracking-wide">
                Admin
              </span>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex flex-col items-center justify-center min-h-[80vh] px-4">
        <div className="max-w-6xl w-full mx-auto mt-14">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {actions.map((action) => (
              <Link
                key={action.title}
                to={action.to}
                className={`
                  group bg-gradient-to-br ${action.gradient}
                  rounded-3xl shadow-2xl p-8 flex flex-col gap-6
                  transition-transform transform hover:-translate-y-1 hover:scale-105
                  hover:shadow-emerald-400/40 focus:outline-none focus:ring-2 focus:ring-indigo-400
                  dark:bg-gradient-to-br dark:from-slate-800 dark:to-slate-900
                  text-white
                `}
                style={{ textDecoration: "none" }}
              >
                <div className="flex items-center gap-5">
                  <div className="p-4 bg-slate-900/70 rounded-2xl shadow-inner">
                    {action.icon}
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold mb-1 drop-shadow-lg">{action.title}</h2>
                    <p className="text-slate-200 text-base opacity-90">{action.description}</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
          {/* Placeholder for future analytics/data views */}
          {/* <section className="mt-16">
            <h3 className="text-xl font-bold mb-4">Analytics & Real-Time Data</h3>
            <div className="rounded-2xl bg-white/80 dark:bg-slate-800/80 p-8 shadow-xl">
              <p className="text-slate-500 dark:text-slate-300">Coming soon...</p>
            </div>
          </section> */}
        </div>
      </main>
    </div>
  );
};

export default AdminDashboard; 