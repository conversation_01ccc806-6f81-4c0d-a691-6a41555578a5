from .admin_auth_api import AdminUserModel  # Use relative import if running as module

EMAIL = "<EMAIL>"
NEW_ROLE = "superadmin"

def update_admin_role():
    admin_model = AdminUserModel()
    user = admin_model.get_admin_by_email(EMAIL)
    if not user:
        print(f"Admin user with email {EMAIL} not found. No update performed.")
        return
    # Update admin role in DB
    with admin_model._get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE admin_users SET role = ? WHERE email = ?
        ''', (NEW_ROLE, EMAIL))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"Admin user {EMAIL} role updated to {NEW_ROLE}.")
        else:
            print(f"Failed to update admin user {EMAIL}.")

if __name__ == "__main__":
    update_admin_role()