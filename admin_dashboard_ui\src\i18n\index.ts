import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enTranslations from './locales/en.json';
import frTranslations from './locales/fr.json';
import esTranslations from './locales/es.json';
import deTranslations from './locales/de.json';
import arTranslations from './locales/ar.json';
import zhTranslations from './locales/zh.json';

// ============================================================================
// SUPPORTED LANGUAGES CONFIGURATION
// ============================================================================

export const supportedLanguages = [
  {
    code: 'en',
    name: 'English',
    native_name: 'English',
    rtl: false,
    flag_emoji: '🇺🇸',
    completion_percentage: 100,
  },
  {
    code: 'fr',
    name: 'French',
    native_name: 'Français',
    rtl: false,
    flag_emoji: '🇫🇷',
    completion_percentage: 95,
  },
  {
    code: 'es',
    name: 'Spanish',
    native_name: 'Español',
    rtl: false,
    flag_emoji: '🇪🇸',
    completion_percentage: 90,
  },
  {
    code: 'de',
    name: 'German',
    native_name: 'Deutsch',
    rtl: false,
    flag_emoji: '🇩🇪',
    completion_percentage: 85,
  },
  {
    code: 'ar',
    name: 'Arabic',
    native_name: 'العربية',
    rtl: true,
    flag_emoji: '🇸🇦',
    completion_percentage: 80,
  },
  {
    code: 'zh',
    name: 'Chinese',
    native_name: '中文',
    rtl: false,
    flag_emoji: '🇨🇳',
    completion_percentage: 75,
  },
];

// ============================================================================
// I18N CONFIGURATION
// ============================================================================

const resources = {
  en: { translation: enTranslations },
  fr: { translation: frTranslations },
  es: { translation: esTranslations },
  de: { translation: deTranslations },
  ar: { translation: arTranslations },
  zh: { translation: zhTranslations },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    // Language detection options
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'i18nextLng',
    },
    
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    
    // Namespace configuration
    defaultNS: 'translation',
    ns: ['translation'],
    
    // React specific options
    react: {
      useSuspense: false,
    },
    
    // Backend options for loading translations
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
  });

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export const getCurrentLanguage = () => i18n.language;

export const changeLanguage = (lng: string) => {
  i18n.changeLanguage(lng);
  
  // Update document direction for RTL languages
  const language = supportedLanguages.find(lang => lang.code === lng);
  if (language) {
    document.documentElement.dir = language.rtl ? 'rtl' : 'ltr';
    document.documentElement.lang = lng;
  }
};

export const isRTL = (lng?: string) => {
  const language = lng || getCurrentLanguage();
  const langConfig = supportedLanguages.find(lang => lang.code === language);
  return langConfig?.rtl || false;
};

export const getLanguageConfig = (lng?: string) => {
  const language = lng || getCurrentLanguage();
  return supportedLanguages.find(lang => lang.code === language);
};

// ============================================================================
// FORMATTING UTILITIES
// ============================================================================

export const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const locale = getCurrentLanguage();
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options,
  };
  
  return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
};

export const formatTime = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const locale = getCurrentLanguage();
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    ...options,
  };
  
  return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
};

export const formatDateTime = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const locale = getCurrentLanguage();
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    ...options,
  };
  
  return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
};

export const formatNumber = (number: number, options?: Intl.NumberFormatOptions) => {
  const locale = getCurrentLanguage();
  return new Intl.NumberFormat(locale, options).format(number);
};

export const formatCurrency = (amount: number, currency = 'USD', options?: Intl.NumberFormatOptions) => {
  const locale = getCurrentLanguage();
  
  const defaultOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency,
    ...options,
  };
  
  return new Intl.NumberFormat(locale, defaultOptions).format(amount);
};

export const formatPercentage = (value: number, options?: Intl.NumberFormatOptions) => {
  const locale = getCurrentLanguage();
  
  const defaultOptions: Intl.NumberFormatOptions = {
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    ...options,
  };
  
  return new Intl.NumberFormat(locale, defaultOptions).format(value / 100);
};

// ============================================================================
// RELATIVE TIME FORMATTING
// ============================================================================

export const formatRelativeTime = (date: Date | string) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  
  const locale = getCurrentLanguage();
  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
  
  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second');
  } else if (diffInSeconds < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
  } else if (diffInSeconds < 86400) {
    return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
  } else if (diffInSeconds < 2592000) {
    return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
  } else if (diffInSeconds < 31536000) {
    return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
  }
};

// ============================================================================
// PLURALIZATION HELPERS
// ============================================================================

export const pluralize = (count: number, singular: string, plural?: string) => {
  if (count === 1) {
    return i18n.t(singular);
  }
  return i18n.t(plural || `${singular}_plural`);
};

// ============================================================================
// TRANSLATION HELPERS
// ============================================================================

export const t = (key: string, options?: any) => i18n.t(key, options);

export const tWithCount = (key: string, count: number, options?: any) => {
  return i18n.t(key, { count, ...options });
};

// Initialize direction on load
const currentLang = getCurrentLanguage();
const langConfig = getLanguageConfig(currentLang);
if (langConfig) {
  document.documentElement.dir = langConfig.rtl ? 'rtl' : 'ltr';
  document.documentElement.lang = currentLang;
}

export default i18n;
