import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  actions: { label: string; onSelect: () => void; icon?: React.ReactNode }[];
}

/**
 * CommandPalette - A modern, accessible command palette (Cmd+K) for quick actions/search.
 * - Keyboard accessible, ARIA support, animated.
 * - Accepts an array of actions (label, onSelect, optional icon).
 * - Closes on outside click or Escape.
 */
const CommandPalette: React.FC<CommandPaletteProps> = ({ isOpen, onClose, actions }) => {
  const paletteRef = useRef<HTMLDivElement>(null);

  // Close on Escape or outside click
  useEffect(() => {
    if (!isOpen) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };
    const handleClick = (e: MouseEvent) => {
      if (paletteRef.current && !paletteRef.current.contains(e.target as Node)) {
        onClose();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleClick);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClick);
    };
  }, [isOpen, onClose]);

  // Focus first action on open
  useEffect(() => {
    if (isOpen && paletteRef.current) {
      const firstBtn = paletteRef.current.querySelector('button');
      if (firstBtn) (firstBtn as HTMLButtonElement).focus();
    }
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-start justify-center bg-black/30 backdrop-blur-sm"
          aria-modal="true"
          role="dialog"
        >
          <motion.div
            ref={paletteRef}
            initial={{ y: -40, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -40, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
            className="mt-32 w-full max-w-lg rounded-2xl bg-white dark:bg-zinc-900 shadow-2xl p-4 border border-zinc-200 dark:border-zinc-700"
          >
            <h2 className="text-lg font-semibold mb-2 text-zinc-900 dark:text-zinc-100">Quick Actions</h2>
            <ul className="space-y-2">
              {actions.map((action, idx) => (
                <li key={idx}>
                  <button
                    className="w-full flex items-center gap-3 px-4 py-2 rounded-lg text-left text-zinc-800 dark:text-zinc-100 hover:bg-blue-50 dark:hover:bg-zinc-800 focus:outline-none focus:ring-2 focus:ring-blue-400"
                    onClick={() => {
                      action.onSelect();
                      onClose();
                    }}
                  >
                    {action.icon && <span>{action.icon}</span>}
                    <span>{action.label}</span>
                  </button>
                </li>
              ))}
            </ul>
            <div className="mt-4 text-xs text-zinc-500 dark:text-zinc-400 text-right">
              Press <kbd className="px-1 py-0.5 bg-zinc-200 dark:bg-zinc-700 rounded">Esc</kbd> to close
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CommandPalette; 