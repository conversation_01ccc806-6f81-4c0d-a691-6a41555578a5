"""
Advanced loss functions for intent classification.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any
import numpy as np


class FocalLoss(nn.Module):
    """
    Focal Loss for addressing class imbalance.
    
    FL(p_t) = -alpha_t * (1 - p_t)^gamma * log(p_t)
    """
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class LabelSmoothingCrossEntropy(nn.Module):
    """
    Label smoothing cross entropy loss.
    """
    
    def __init__(self, epsilon: float = 0.1, reduction: str = 'mean'):
        super().__init__()
        self.epsilon = epsilon
        self.reduction = reduction
        
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        num_classes = inputs.size(-1)
        log_probs = F.log_softmax(inputs, dim=-1)
        
        # Create smoothed targets
        targets_one_hot = torch.zeros_like(inputs).scatter_(
            1, targets.unsqueeze(1), 1
        )
        smoothed_targets = targets_one_hot * (1 - self.epsilon) + self.epsilon / num_classes
        
        loss = -(smoothed_targets * log_probs).sum(dim=-1)
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss


class ContrastiveLoss(nn.Module):
    """
    Contrastive loss for learning better embeddings.
    """
    
    def __init__(self, temperature: float = 0.1, margin: float = 0.5):
        super().__init__()
        self.temperature = temperature
        self.margin = margin
        
    def forward(self, embeddings: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        Args:
            embeddings: (batch_size, embedding_dim)
            labels: (batch_size,)
        """
        # Normalize embeddings
        embeddings = F.normalize(embeddings, p=2, dim=1)
        
        # Compute similarity matrix
        similarity_matrix = torch.matmul(embeddings, embeddings.T) / self.temperature
        
        # Create mask for positive pairs (same label)
        labels = labels.unsqueeze(0)
        positive_mask = (labels == labels.T).float()
        
        # Remove self-similarity
        positive_mask.fill_diagonal_(0)
        
        # Create mask for negative pairs (different labels)
        negative_mask = 1 - positive_mask
        negative_mask.fill_diagonal_(0)
        
        # Compute positive and negative similarities
        positive_similarities = similarity_matrix * positive_mask
        negative_similarities = similarity_matrix * negative_mask
        
        # Find hardest negative for each positive
        hardest_negative_similarities = negative_similarities.max(dim=1)[0]
        
        # Compute contrastive loss
        positive_similarities = positive_similarities.sum(dim=1)
        num_positives = positive_mask.sum(dim=1)
        
        # Avoid division by zero
        num_positives = torch.clamp(num_positives, min=1)
        positive_similarities = positive_similarities / num_positives
        
        # Contrastive loss: maximize positive similarity, minimize negative similarity
        loss = -positive_similarities + torch.clamp(hardest_negative_similarities + self.margin, min=0)
        
        return loss.mean()


class TripletLoss(nn.Module):
    """
    Triplet loss for learning embeddings with better separation.
    """
    
    def __init__(self, margin: float = 0.5, mining: str = 'hard'):
        super().__init__()
        self.margin = margin
        self.mining = mining
        
    def forward(self, embeddings: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        Args:
            embeddings: (batch_size, embedding_dim)
            labels: (batch_size,)
        """
        # Normalize embeddings
        embeddings = F.normalize(embeddings, p=2, dim=1)
        
        # Compute pairwise distances
        dist_matrix = torch.cdist(embeddings, embeddings)
        
        # Find positive and negative pairs
        labels = labels.unsqueeze(0)
        positive_mask = (labels == labels.T).float()
        negative_mask = 1 - positive_mask
        
        # Remove self-distances
        positive_mask.fill_diagonal_(0)
        negative_mask.fill_diagonal_(0)
        
        if self.mining == 'hard':
            # Hard negative mining
            positive_distances = dist_matrix * positive_mask
            negative_distances = dist_matrix * negative_mask
            
            # Find hardest positive and negative for each anchor
            hardest_positive_distances = positive_distances.max(dim=1)[0]
            hardest_negative_distances = negative_distances.min(dim=1)[0]
            
            # Compute triplet loss
            loss = torch.clamp(
                hardest_positive_distances - hardest_negative_distances + self.margin, 
                min=0
            )
            
        else:
            # Semi-hard negative mining
            positive_distances = dist_matrix * positive_mask
            negative_distances = dist_matrix * negative_mask
            
            # Find positive and negative distances
            positive_distances = positive_distances.sum(dim=1)
            num_positives = positive_mask.sum(dim=1)
            num_positives = torch.clamp(num_positives, min=1)
            positive_distances = positive_distances / num_positives
            
            # Find semi-hard negatives (closer than positive + margin)
            semi_hard_mask = (negative_distances < positive_distances.unsqueeze(1) + self.margin) * negative_mask
            semi_hard_distances = negative_distances * semi_hard_mask
            
            # If no semi-hard negatives, use hardest negative
            no_semi_hard = (semi_hard_distances.sum(dim=1) == 0)
            hardest_negative_distances = negative_distances.min(dim=1)[0]
            semi_hard_distances[no_semi_hard] = hardest_negative_distances[no_semi_hard].unsqueeze(1)
            
            # Compute loss
            loss = torch.clamp(
                positive_distances - semi_hard_distances.min(dim=1)[0] + self.margin,
                min=0
            )
        
        return loss.mean()


class CombinedLoss(nn.Module):
    """
    Combined loss function that can mix multiple loss types.
    """
    
    def __init__(self, loss_config: Dict[str, Any]):
        super().__init__()
        self.loss_config = loss_config
        self.losses = {}
        self.weights = {}
        
        # Initialize loss functions
        for loss_name, config in loss_config.items():
            if loss_name == 'cross_entropy':
                self.losses[loss_name] = nn.CrossEntropyLoss()
                self.weights[loss_name] = config.get('weight', 1.0)
            elif loss_name == 'focal':
                self.losses[loss_name] = FocalLoss(
                    alpha=config.get('alpha', 1.0),
                    gamma=config.get('gamma', 2.0)
                )
                self.weights[loss_name] = config.get('weight', 1.0)
            elif loss_name == 'label_smoothing':
                self.losses[loss_name] = LabelSmoothingCrossEntropy(
                    epsilon=config.get('epsilon', 0.1)
                )
                self.weights[loss_name] = config.get('weight', 1.0)
            elif loss_name == 'contrastive':
                self.losses[loss_name] = ContrastiveLoss(
                    temperature=config.get('temperature', 0.1),
                    margin=config.get('margin', 0.5)
                )
                self.weights[loss_name] = config.get('weight', 0.1)
            elif loss_name == 'triplet':
                self.losses[loss_name] = TripletLoss(
                    margin=config.get('margin', 0.5),
                    mining=config.get('mining', 'hard')
                )
                self.weights[loss_name] = config.get('weight', 0.1)
    
    def forward(self, outputs: Dict[str, torch.Tensor], targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            outputs: Dictionary containing different model outputs
            targets: Target labels
        """
        total_loss = 0.0
        
        for loss_name, loss_fn in self.losses.items():
            if loss_name in ['contrastive', 'triplet']:
                # These losses work on embeddings
                if 'embeddings' in outputs:
                    loss = loss_fn(outputs['embeddings'], targets)
                else:
                    continue
            else:
                # Classification losses work on logits
                if 'logits' in outputs:
                    loss = loss_fn(outputs['logits'], targets)
                else:
                    continue
            
            total_loss += self.weights[loss_name] * loss
        
        return total_loss


def get_loss_function(loss_config: Dict[str, Any]) -> nn.Module:
    """
    Factory function to create loss function from config.
    """
    loss_type = loss_config.get('type', 'cross_entropy')
    
    if loss_type == 'cross_entropy':
        return nn.CrossEntropyLoss()
    elif loss_type == 'focal':
        return FocalLoss(
            alpha=loss_config.get('focal', {}).get('alpha', 1.0),
            gamma=loss_config.get('focal', {}).get('gamma', 2.0)
        )
    elif loss_type == 'label_smoothing':
        return LabelSmoothingCrossEntropy(
            epsilon=loss_config.get('label_smoothing', {}).get('epsilon', 0.1)
        )
    elif loss_type == 'combined':
        return CombinedLoss(loss_config.get('combined', {}))
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")


class ClassBalancedLoss(nn.Module):
    """
    Class-balanced loss for handling imbalanced datasets.
    """
    
    def __init__(self, class_counts: torch.Tensor, beta: float = 0.9999):
        super().__init__()
        self.beta = beta
        self.class_counts = class_counts
        self.effective_num = 1.0 - torch.pow(beta, class_counts)
        self.weights = (1.0 - beta) / self.effective_num
        self.weights = self.weights / self.weights.sum() * len(class_counts)
        
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        weights = self.weights[targets]
        return (weights * ce_loss).mean() 