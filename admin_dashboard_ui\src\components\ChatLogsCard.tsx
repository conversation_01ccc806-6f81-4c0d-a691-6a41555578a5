import React, { useEffect, useState } from "react";
import { MessageCircle } from "lucide-react";
import { API_BASE_URL } from "../apiConfig";

export function ChatLogsCard() {
  const [logs, setLogs] = useState([]);
  const [deviceId, setDeviceId] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [loading, setLoading] = useState(false);

  const fetchLogs = () => {
    setLoading(true);
    const params = new URLSearchParams();
    if (deviceId) params.append("device_id", deviceId);
    if (startDate) params.append("start_date", startDate);
    if (endDate) params.append("end_date", endDate);
    fetch(`/api/chatlogs?${params.toString()}`)
      .then(res => res.json())
      .then(data => setLogs(data))
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchLogs();
    // eslint-disable-next-line
  }, []);

  return (
    <div className="bg-neutral-900 rounded-xl shadow-lg p-6 border border-neutral-800">
      <div className="flex items-center mb-4">
        <MessageCircle className="w-6 h-6 text-accent-400 mr-2" />
        <span className="font-semibold text-lg">Chat Logs</span>
      </div>
      <div className="flex flex-wrap gap-2 mb-4">
        <input
          type="text"
          placeholder="User/Session (device_id)"
          value={deviceId}
          onChange={e => setDeviceId(e.target.value)}
          className="bg-neutral-800 rounded px-2 py-1 text-sm text-neutral-100"
        />
        <input
          type="date"
          value={startDate}
          onChange={e => setStartDate(e.target.value)}
          className="bg-neutral-800 rounded px-2 py-1 text-sm text-neutral-100"
        />
        <input
          type="date"
          value={endDate}
          onChange={e => setEndDate(e.target.value)}
          className="bg-neutral-800 rounded px-2 py-1 text-sm text-neutral-100"
        />
        <button
          onClick={fetchLogs}
          className="bg-accent-400 text-neutral-900 rounded px-3 py-1 text-sm font-semibold hover:bg-accent-600"
        >
          Filter
        </button>
      </div>
      {loading ? (
        <div className="text-neutral-400">Loading...</div>
      ) : (
        <ul className="space-y-2 max-h-96 overflow-y-auto">
          {logs.map((log: any, idx: number) => (
            <li key={log.id || idx} className="flex flex-col bg-neutral-800 rounded p-2">
              <div className="flex justify-between">
                <span className="font-semibold">{log.device_id}</span>
                <span className="text-xs text-neutral-400">{log.query_timestamp?.slice(0, 19).replace('T', ' ')}</span>
              </div>
              <div className="text-neutral-300"><b>User:</b> {log.user_query}</div>
              <div className="text-neutral-300"><b>Bot:</b> {log.assistant_response}</div>
              <div className="text-xs text-neutral-500">Lang: {log.language}</div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
} 