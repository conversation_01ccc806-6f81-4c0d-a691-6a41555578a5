import React from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  FileText, 
  Users, 
  Home, 
  Briefcase, 
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface HRSuggestion {
  title: string;
  description: string;
  query: string;
  icon: React.ReactNode;
  color: string;
}

interface HRSuggestionButtonsProps {
  onSuggestionClick: (query: string) => void;
  className?: string;
}

const HRSuggestionButtons: React.FC<HRSuggestionButtonsProps> = ({
  onSuggestionClick,
  className
}) => {
  const suggestions: HRSuggestion[] = [
    {
      title: "Leave Policy",
      description: "Learn about vacation, sick leave, and time-off policies",
      query: "What is the company's leave policy and how do I request time off?",
      icon: <Calendar className="w-6 h-6" />,
      color: "bg-blue-500"
    },
    {
      title: "Referral Program", 
      description: "Understand how employee referrals work and rewards",
      query: "How does the employee referral program work and what are the benefits?",
      icon: <Users className="w-6 h-6" />,
      color: "bg-green-500"
    },
    {
      title: "Dress Code",
      description: "Check the company dress code and appearance guidelines",
      query: "What is the company dress code policy?",
      icon: <FileText className="w-6 h-6" />,
      color: "bg-purple-500"
    },
    {
      title: "Work from Home",
      description: "Remote work policies and hybrid work arrangements",
      query: "Tell me about the work from home and remote work policies",
      icon: <Home className="w-6 h-6" />,
      color: "bg-orange-500"
    },
    {
      title: "Leave Balance",
      description: "Get your current leave balances instantly",
      query: "Get my current Leave Balances",
      icon: <Clock className="w-6 h-6" />,
      color: "bg-pink-500"
    },
    {
      title: "Download Payslip",
      description: "Download your payslip for a specific month",
      query: "Download my Payslip for June 2025",
      icon: <Briefcase className="w-6 h-6" />,
      color: "bg-yellow-500"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 150,
        damping: 25,
        mass: 0.6
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-w-4xl mx-auto px-4", className)}
    >
      {suggestions.map((suggestion, index) => (
        <motion.button
          key={index}
          variants={cardVariants}
          onClick={() => onSuggestionClick(suggestion.query)}
          className={cn(
            "relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
            "shadow-sm transition-all duration-200 hover:-translate-y-1 hover:scale-[1.03] hover:shadow-lg",
            "text-left focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-1",
            "group p-3 h-[90px] flex items-center justify-between",
            "w-full",
            className
          )}
        >
          <div className="relative z-10 flex items-center gap-3 flex-1 min-w-0">
            <div className={cn(
              "p-2 rounded-lg text-white flex-shrink-0 shadow-sm",
              suggestion.color
            )}>
              {React.cloneElement(suggestion.icon as React.ReactElement, { className: "w-4 h-4" })}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 dark:text-white mb-1 text-sm leading-tight truncate">
                {suggestion.title}
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 leading-tight line-clamp-2">
                {suggestion.description}
              </p>
            </div>
          </div>

          {/* Subtle arrow indicator */}
          <div className="relative z-10 flex-shrink-0 ml-2">
            <div className="w-5 h-5 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center transition-all duration-200">
              <svg className="w-2.5 h-2.5 text-gray-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </motion.button>
      ))}
    </motion.div>
  );
};

export default HRSuggestionButtons;
