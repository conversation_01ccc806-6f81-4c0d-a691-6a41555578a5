import React from "react";

type SliderProps = React.InputHTMLAttributes<HTMLInputElement> & {
  onValueChange?: (value: number) => void;
};

export const Slider = React.forwardRef<HTMLInputElement, SliderProps>(
  ({ value, onChange, onValueChange, min = 0, max = 100, step = 1, ...props }, ref) => (
    <input
      ref={ref}
      type="range"
      value={value}
      min={min}
      max={max}
      step={step}
      onChange={e => {
        onChange?.(e);
        onValueChange?.(Number(e.target.value));
      }}
      {...props}
    />
  )
);
Slider.displayName = 'Slider'; 