import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Select } from '../ui/select';
import toast from 'react-hot-toast';

interface RoleOption {
  id: number;
  name: string;
  level: number;
}

interface AddUserModalProps {
  onClose: () => void;
  onAdd: (userData: any) => void;
  canAssignRole: (roleLevel: number) => boolean;
}

const AddUserModal: React.FC<AddUserModalProps> = ({ onClose, onAdd, canAssignRole }) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [roleId, setRoleId] = useState<number | null>(null);
  const [tenant, setTenant] = useState('');
  const [roles, setRoles] = useState<RoleOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    async function fetchRoles() {
      try {
        const res = await fetch('/api/roles');
        if (!res.ok) throw new Error('Failed to fetch roles');
        const data = await res.json();
        // Assume roles are returned as [{id, name, level}]
        setRoles(data);
      } catch (e: any) {
        toast.error(e.message);
      }
    }
    fetchRoles();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !email || !roleId) {
      toast.error('Please fill all required fields');
      return;
    }
    setLoading(true);
    try {
      const selectedRole = roles.find(r => r.id === roleId);
      if (!selectedRole) throw new Error('Invalid role selected');
      await onAdd({ full_name: name, email, role: selectedRole.name, tenant_id: tenant || undefined });
    } catch (e: any) {
      toast.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
      <div className="bg-white dark:bg-gray-900 rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-bold mb-4">Add Admin User</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block mb-1">Name</label>
            <input className="w-full border rounded px-2 py-1" value={name} onChange={e => setName(e.target.value)} required />
          </div>
          <div>
            <label className="block mb-1">Email</label>
            <input className="w-full border rounded px-2 py-1" type="email" value={email} onChange={e => setEmail(e.target.value)} required />
          </div>
          <div>
            <label className="block mb-1">Role</label>
            <select className="w-full border rounded px-2 py-1" value={roleId ?? ''} onChange={e => setRoleId(Number(e.target.value))} required>
              <option value="" disabled>Select role</option>
              {roles.filter(r => canAssignRole(r.level)).map(role => (
                <option key={role.id} value={role.id}>{role.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block mb-1">Tenant (optional)</label>
            <input className="w-full border rounded px-2 py-1" value={tenant} onChange={e => setTenant(e.target.value)} />
          </div>
          <div className="flex justify-end gap-2 mt-4">
            <Button type="button" variant="secondary" onClick={onClose}>Cancel</Button>
            <Button type="submit" disabled={loading}>{loading ? 'Adding...' : 'Add User'}</Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddUserModal; 