import { useEffect, useState } from "react";
import { BarChart, Bar, Line, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";
import { getChatTrends } from '@/services/api';

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border border-border rounded-lg shadow-lg p-3">
        <p className="text-sm font-medium text-popover-foreground">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm text-muted-foreground">
            <span 
              className="inline-block w-3 h-3 rounded mr-2" 
              style={{ backgroundColor: entry.color }}
            />
            {entry.name}: <span className="font-medium">{entry.value}</span>
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const ChatsPerDayChart = ({ dateRange }: { dateRange: string }) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    getChatTrends()
      .then(res => setData(res.data))
      .catch(() => setError('Failed to load data'))
      .finally(() => setLoading(false));
  }, [dateRange]);

  if (loading) return <Skeleton className="h-64 w-full rounded-lg" />;
  if (error) return (
    <div className="h-64 w-full flex items-center justify-center text-red-500 bg-red-50 dark:bg-red-900/20 rounded-lg">
      {error}
    </div>
  );
  if (!data.length) return (
    <div className="h-64 w-full flex items-center justify-center text-muted-foreground bg-muted/30 rounded-lg">
      No data available.
    </div>
  );

  return (
    <div className="h-64 w-full rounded-xl bg-white dark:bg-zinc-900">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 16, right: 16, left: 0, bottom: 0 }}>
          <CartesianGrid 
            strokeDasharray="3 3" 
            stroke="hsl(var(--muted-foreground))" 
            opacity={0.3}
          />
          <XAxis 
            dataKey="date" 
            stroke="hsl(var(--muted-foreground))" 
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <YAxis 
            stroke="hsl(var(--muted-foreground))" 
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `${value}`}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend 
            verticalAlign="top" 
            height={36}
            wrapperStyle={{
              fontSize: '12px',
              color: 'hsl(var(--muted-foreground))'
            }}
          />
          <Bar 
            dataKey="chats" 
            fill="var(--chart-primary)" 
            name="Chats" 
            radius={[4, 4, 0, 0]} 
            opacity={0.9}
          />
          <Line 
            type="monotone" 
            dataKey="avg" 
            stroke="var(--chart-secondary)" 
            name="7d Avg" 
            strokeWidth={3} 
            dot={{ fill: "var(--chart-secondary)", strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: "var(--chart-secondary)", strokeWidth: 2 }}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ChatsPerDayChart; 