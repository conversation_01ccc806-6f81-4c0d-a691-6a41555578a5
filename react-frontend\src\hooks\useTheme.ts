import { useState, useEffect } from 'react';
import { ThemeMode } from '@/types';

export const useTheme = () => {
  const [theme, setTheme] = useState<ThemeMode['mode']>('light');

  useEffect(() => {
    // Load settings from localStorage like original
    const loadSettings = () => {
      try {
        const savedSettings = localStorage.getItem('hr_assistant_settings');
        return savedSettings ? JSON.parse(savedSettings) : { theme: 'system' };
      } catch (error) {
        console.error('Error loading settings:', error);
        return { theme: 'system' };
      }
    };

    const settings = loadSettings();
    const savedTheme = settings.theme || 'system';
    setTheme(savedTheme);
    applyTheme(savedTheme);
  }, []);

  const applyTheme = (newTheme: ThemeMode['mode']) => {
    const root = document.documentElement;
    const body = document.body;

    // Determine actual theme
    let actualTheme = newTheme;
    if (newTheme === 'system') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      actualTheme = prefersDark ? 'dark' : 'light';
    }

    // Apply theme classes exactly like original
    if (actualTheme === 'dark') {
      root.classList.add('dark');
      body.classList.add('theme-dark');
      body.classList.remove('theme-light');
    } else {
      root.classList.remove('dark');
      body.classList.add('theme-light');
      body.classList.remove('theme-dark');
    }

    // Dispatch theme change event like original
    const themeEvent = new CustomEvent('themeChanged', {
      detail: { theme: actualTheme }
    });
    document.dispatchEvent(themeEvent);
  };

  const changeTheme = (newTheme: ThemeMode['mode']) => {
    setTheme(newTheme);

    // Save to localStorage like original
    const settings = {
      theme: newTheme,
      language: 'auto-detect'
    };
    localStorage.setItem('hr_assistant_settings', JSON.stringify(settings));

    applyTheme(newTheme);
  };

  // Listen for system theme changes when in system mode
  useEffect(() => {
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => applyTheme('system');
      
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme]);

  return {
    theme,
    changeTheme,
    isDark: theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches),
  };
};
