import React, { Suspense, useContext } from "react";
import { PermissionsContext } from "../../context/PermissionsProvider";

const MetricsOverview = React.lazy(() => import("../../components/dashboard/MetricsOverview"));

const ALLOWED_ROLES = ["SUPERADMIN", "ADMIN", "HR_LEAD", "VIEWER"];

const DashboardLanding: React.FC = () => {
  const permissions = useContext(PermissionsContext);
  if (!permissions) throw new Error("PermissionsContext is missing provider!");
  const { role, loading } = permissions;

  if (loading) return <div>Loading dashboard...</div>;

  if (!role || !ALLOWED_ROLES.includes(role.toUpperCase())) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-12">
        <h2 className="text-2xl font-bold mb-4">No Access</h2>
        <p className="text-muted-foreground">You do not have permission to view this dashboard. Please contact your administrator.</p>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginTop: 40 }}>
        <Suspense fallback={<div>Loading metrics overview...</div>}>
          <MetricsOverview />
        </Suspense>
      </div>
    </div>
  );
};

export default DashboardLanding; 