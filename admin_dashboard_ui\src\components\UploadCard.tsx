import { useRef, useState } from "react";
import { FileUp } from "lucide-react";
import { motion } from "framer-motion";
import MotionDiv from './ui/MotionDiv';
import { API_BASE_URL } from "../apiConfig";

export function UploadCard() {
  const [dragActive, setDragActive] = useState(false);
  const [fileName, setFileName] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      uploadFile(e.dataTransfer.files[0]);
    }
  };

  const uploadFile = (file: File) => {
    setFileName(file.name);
    const formData = new FormData();
    formData.append("file", file);

    fetch(`${API_BASE_URL}/api/upload`, {
      method: "POST",
      body: formData,
    })
      .then(res => res.json())
      .then(data => setStatus(data.status === "success" ? "Uploaded!" : "Failed"))
      .catch(() => setStatus("Failed"));
  };

  return (
    <MotionDiv
      className="bg-neutral-900 rounded-xl shadow-lg p-6 flex flex-col items-center border border-neutral-800"
      whileHover={{ scale: 1.02 }}>
      <div
        className={`w-full flex flex-col items-center justify-center border-2 ${dragActive ? "border-accent-400" : "border-neutral-700"} border-dashed rounded-lg p-8 transition-all`}
        onDragOver={e => { e.preventDefault(); setDragActive(true); }}
        onDragLeave={e => { e.preventDefault(); setDragActive(false); }}
        onDrop={handleDrop}
        onClick={() => inputRef.current?.click()}
        tabIndex={0}
        role="button"
        aria-label="Upload HR Document"
      >
        <FileUp className="w-10 h-10 mb-2 text-accent-400" />
        <span className="font-semibold text-lg mb-2">Upload HR Document</span>
        <span className="text-neutral-400 mb-2">Drag & drop or click to select</span>
        <input
          ref={inputRef}
          type="file"
          className="hidden"
          onChange={e => {
            if (e.target.files && e.target.files[0]) {
              uploadFile(e.target.files[0]);
            }
          }}
        />
        {fileName && (
          <span className="mt-2 text-accent-400">{fileName} <span className="text-xs text-neutral-400">(Ready to upload)</span></span>
        )}
        {status && (
          <span className={`mt-2 text-sm ${status === "Uploaded!" ? "text-green-400" : "text-red-400"}`}>{status}</span>
        )}
      </div>
    </MotionDiv>
  );
} 