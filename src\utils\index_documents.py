#!/usr/bin/env python3
"""
Utility functions for indexing documents into the vector database.
Can be used both as a standalone script and as an imported function.
"""

import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import time

from ..document_processing.embedding_generator import EmbeddingGenerator
from ..database.vector_store import QdrantVectorStore as VectorStore
from ..config import DATA_DIR, PROCESSED_DIR
from ..utils.logger import get_logger

logger = get_logger(__name__)

class DocumentIndexer:
    """Indexes processed documents into the vector store."""
    
    def __init__(self):
        self.embedding_generator = EmbeddingGenerator()
        self.vector_store = VectorStore()
        logger.info("[OK] Embedding generator initialized")
        logger.info("[OK] Vector store initialized")
    
    def index_processed_documents(self, processed_dir: Path = None) -> Dict[str, Any]:
        """
        Index all processed documents from the processed directory.
        
        Args:
            processed_dir: Directory containing processed documents
            
        Returns:
            Dictionary with indexing statistics
        """
        if processed_dir is None:
            processed_dir = PROCESSED_DIR
        
        if not processed_dir.exists():
            logger.error(f"Processed directory does not exist: {processed_dir}")
            return {"success": False, "error": "Directory not found"}
        
        chunk_files = list(processed_dir.glob("*.chunks.json"))
        total_chunks = 0
        indexed_chunks = 0
        
        logger.info(f"Found {len(chunk_files)} chunk files to index")
        
        for chunk_file in chunk_files:
            try:
                with open(chunk_file, 'r', encoding='utf-8') as f:
                    chunks = json.load(f)
                
                # Index chunks
                self._index_chunks(chunks, chunk_file.name)
                indexed_chunks += len(chunks)
                total_chunks += len(chunks)
                
                logger.info(f"[OK] Indexed {len(chunks)} chunks from {chunk_file.name}")
                
            except Exception as e:
                logger.error(f"[ERROR] Error processing {chunk_file.name}: {e}")
                continue
        
        if indexed_chunks > 0:
            logger.info(f"[SUCCESS] Indexing complete! Indexed {indexed_chunks}/{total_chunks} chunks")
            return {
                "success": True,
                "indexed_chunks": indexed_chunks,
                "total_chunks": total_chunks
            }
        else:
            logger.error(f"[ERROR] Indexing failed: {e}")
            return {"success": False, "error": "No chunks indexed"}
    
    def index_single_document(self, source_file: str, chunks: List[Dict[str, Any]]) -> bool:
        """
        Index a single document's chunks.
        
        Args:
            source_file: Source file name
            chunks: List of chunks to index
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self._index_chunks(chunks, source_file)
            logger.info(f"[OK] Indexed {len(chunks)} chunks from {source_file}")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to index {source_file}: {e}")
            return False
    
    def _index_chunks(self, chunks: List[Dict[str, Any]], source_name: str):
        """
        Index chunks into the vector store.
        
        Args:
            chunks: List of chunks to index
            source_name: Name of the source document
        """
        for chunk in chunks:
            if not chunk.get('text'):
                continue
            
            # Generate embedding
            embedding = self.embedding_generator.generate_embedding(chunk['text'])
            
            # Prepare metadata
            metadata = {
                'source': source_name,
                'chunk_id': chunk.get('id', ''),
                'start_char': chunk.get('start_char', 0),
                'end_char': chunk.get('end_char', 0),
                'page': chunk.get('page', 0),
                'section': chunk.get('section', ''),
                'file_type': chunk.get('file_type', ''),
                'confidence': chunk.get('confidence', 0.0)
            }
            
            # Add to vector store
            self.vector_store.add_document(
                text=chunk['text'],
                embedding=embedding,
                metadata=metadata
            )

def index_all_documents():
    """Index all processed documents."""
    indexer = DocumentIndexer()
    return indexer.index_processed_documents()

def index_specific_document(source_file: str, chunks: List[Dict[str, Any]]):
    """Index a specific document."""
    indexer = DocumentIndexer()
    return indexer.index_single_document(source_file, chunks)

def get_indexed_document_count():
    """Get the count of indexed documents."""
    indexer = DocumentIndexer()
    info = indexer.vector_store.get_collection_info()
    return info.get('points_count', 0)

def main():
    """Main function to run document indexing."""
    indexer = DocumentIndexer()
    
    try:
        result = indexer.index_processed_documents()
        if result["success"]:
            print("[OK] Document indexing completed successfully!")
        else:
            print("[ERROR] Document indexing failed!")
    except Exception as e:
        print(f"[ERROR] Indexing error: {e}")

if __name__ == "__main__":
    main() 