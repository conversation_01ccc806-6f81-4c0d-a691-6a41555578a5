import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DateRangePicker } from "@/components/ui/date-range-picker"; // Assume stub
import { Select } from "@/components/ui/select"; // Assume stub
import { Skeleton } from "@/components/ui/skeleton";
import { useReactTable, getCoreRowModel, flexRender, ColumnDef, getSortedRowModel, SortingState } from "@tanstack/react-table";
import api from "@/services/api";

const PAGE_SIZE = 10;
const CHANGE_TYPES = ["All", "Consent Given", "Consent Withdrawn"];

const columns: ColumnDef<any>[] = [
  { accessorKey: "user_email", header: "User Email" },
  { accessorKey: "change_type", header: "Change Type" },
  { accessorKey: "timestamp", header: "Timestamp" },
];

const GDPRLogs = () => {
  const [dateRange, setDateRange] = useState("7d");
  const [changeType, setChangeType] = useState("All");
  const [page, setPage] = useState(1);
  const [data, setData] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = [
      `range=${dateRange}`,
      changeType !== "All" ? `type=${encodeURIComponent(changeType)}` : null,
      `page=${page}`,
      `pageSize=${PAGE_SIZE}`,
    ]
      .filter(Boolean)
      .join("&");
    api
      .get(`/compliance/gdpr-logs?${params}`)
      .then((res) => {
        setData(res.data.logs || []);
        setTotal(res.data.total || 0);
      })
      .catch((err) => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [dateRange, changeType, page]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: { sorting },
    onSortingChange: setSorting,
    manualSorting: false,
  });

  const handleExportCSV = () => {
    alert("Export to CSV (mock)");
  };
  const handleExportJSON = () => {
    alert("Export to JSON (mock)");
  };

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  const totalPages = Math.ceil(total / PAGE_SIZE);

  return (
    <div className="max-w-5xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle>GDPR & Consent Logs</CardTitle>
          <div className="flex flex-wrap gap-2 items-center">
            <Select value={dateRange} onValueChange={setDateRange}>
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="custom">Custom</option>
            </Select>
            <Select value={changeType} onValueChange={setChangeType}>
              {CHANGE_TYPES.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
            <Button size="sm" variant="outline" onClick={handleExportCSV}>Export CSV</Button>
            <Button size="sm" variant="outline" onClick={handleExportJSON}>Export JSON</Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
              <thead className="bg-muted dark:bg-zinc-800">
                {table.getHeaderGroups().map(headerGroup => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <th
                        key={header.id}
                        className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase cursor-pointer"
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getIsSorted() ? (header.column.getIsSorted() === "asc" ? " ▲" : " ▼") : ""}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody className="bg-background dark:bg-zinc-900">
                {table.getRowModel().rows.map(row => (
                  <tr key={row.id} className="border-b border-border dark:border-zinc-800">
                    {row.getVisibleCells().map(cell => (
                      <td key={cell.id} className="px-4 py-2 text-sm">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {/* Pagination controls */}
          <div className="flex justify-end gap-2 items-center mt-4">
            <Button size="sm" variant="ghost" disabled={page === 1} onClick={() => setPage(page - 1)}>
              Previous
            </Button>
            <span className="text-xs text-muted-foreground">
              Page {page} of {totalPages || 1}
            </span>
            <Button size="sm" variant="ghost" disabled={page === totalPages || totalPages === 0} onClick={() => setPage(page + 1)}>
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GDPRLogs; 