import React from "react";

type DateRange = {
  from: Date | undefined;
  to: Date | undefined;
};

type DateRangePickerProps = {
  value?: DateRange;
  onChange?: (range: DateRange) => void;
  className?: string;
};

export const DateRangePicker = React.forwardRef<HTMLDivElement, DateRangePickerProps>(
  ({ value, onChange, className, ...props }, ref) => {
    const handleFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const fromDate = e.target.value ? new Date(e.target.value) : undefined;
      onChange?.({
        from: fromDate,
        to: value?.to
      });
    };

    const handleToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const toDate = e.target.value ? new Date(e.target.value) : undefined;
      onChange?.({
        from: value?.from,
        to: toDate
      });
    };

    return (
      <div ref={ref} className={`flex flex-col gap-2 ${className || ''}`} {...props}>
        <div className="flex items-center gap-2">
          <label className="text-xs font-medium text-muted-foreground">From:</label>
          <input
            type="date"
            value={value?.from ? value.from.toISOString().split('T')[0] : ''}
            onChange={handleFromChange}
            className="px-2 py-1 text-xs border rounded bg-background"
          />
        </div>
        <div className="flex items-center gap-2">
          <label className="text-xs font-medium text-muted-foreground">To:</label>
          <input
            type="date"
            value={value?.to ? value.to.toISOString().split('T')[0] : ''}
            onChange={handleToChange}
            className="px-2 py-1 text-xs border rounded bg-background"
          />
        </div>
      </div>
    );
  }
);

DateRangePicker.displayName = 'DateRangePicker'; 