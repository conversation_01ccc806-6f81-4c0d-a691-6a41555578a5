import React from "react";

type ToggleProps = {
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  children?: React.ReactNode;
};

export const Toggle = React.forwardRef<HTMLInputElement, ToggleProps>(
  ({ checked, onCheckedChange, children, ...props }, ref) => (
    <label style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}>
      <input
        ref={ref}
        type="checkbox"
        checked={checked}
        onChange={e => onCheckedChange?.(e.target.checked)}
        style={{ marginRight: 8 }}
        {...props as any}
      />
      {children}
    </label>
  )
);
Toggle.displayName = 'Toggle'; 