import { create } from "zustand";
import { persist } from "zustand/middleware";
import { jwtDecode } from "jwt-decode";
import { AdminUser, Role, Roles } from '@/types';

// ============================================================================
// AUTHENTICATION STATE INTERFACE
// ============================================================================

interface AuthState {
  // Authentication status
  isAuthenticated: boolean;
  isLoading: boolean;

  // User data
  user: AdminUser | null;
  token: string | null;
  refreshToken: string | null;

  // Session management
  sessionExpiry: number | null;
  lastActivity: number;

  // 2FA state
  requires2FA: boolean;
  twoFactorToken: string | null;

  // Actions
  login: (token: string, refreshToken: string, user: AdminUser) => void;
  logout: () => void;
  setUser: (user: AdminUser) => void;
  setToken: (token: string) => void;
  setRefreshToken: (refreshToken: string) => void;
  updateLastActivity: () => void;
  set2FARequired: (required: boolean, token?: string) => void;
  clearSession: () => void;

  // Utilities
  isTokenExpired: () => boolean;
  getTimeUntilExpiry: () => number;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: Role) => boolean;
  role: Role | null;
  setRole: (role: Role) => void;
}

// ============================================================================
// JWT UTILITIES
// ============================================================================

const isTokenValid = (token: string | null): boolean => {
  if (!token) return false;

  try {
    const decoded: any = jwtDecode(token);
    const currentTime = Date.now() / 1000;
    return decoded.exp > currentTime;
  } catch {
    return false;
  }
};

const getTokenExpiry = (token: string | null): number | null => {
  if (!token) return null;

  try {
    const decoded: any = jwtDecode(token);
    return decoded.exp * 1000; // Convert to milliseconds
  } catch {
    return null;
  }
};

const getUserFromToken = (token: string | null): Partial<AdminUser> | null => {
  if (!token) return null;

  try {
    const decoded: any = jwtDecode(token);
    return {
      id: decoded.sub,
      email: decoded.email,
      role: decoded.role,
      organization_id: decoded.org_id,
    };
  } catch {
    return null;
  }
};

// ============================================================================
// ROLE-BASED PERMISSIONS
// ============================================================================

const rolePermissions: Record<string, string[]> = {
  SUPERADMIN: [
    'admin:*',
    'user:*',
    'system:*',
    'audit:*',
    'metrics:*',
    'chat:*',
    'feedback:*',
    'integration:*',
    'export:*',
  ],
  ADMIN: [
    'user:read',
    'user:create',
    'user:update',
    'audit:read',
    'metrics:read',
    'chat:read',
    'chat:escalate',
    'feedback:read',
    'feedback:create',
    'export:read',
  ],
  SUPPORT_AGENT: [
    'chat:read',
    'chat:escalate',
    'chat:resolve',
    'feedback:read',
    'feedback:create',
    'metrics:read',
  ],
  COMPLIANCE_AUDITOR: [
    'audit:read',
    'user:read',
    'chat:read',
    'export:read',
    'compliance:*',
  ],
  DEVELOPER: [
    'system:read',
    'integration:*',
    'metrics:read',
    'audit:read',
  ],
  HR_LEAD: [
    'user:read',
    'metrics:read',
    'chat:read',
    'feedback:read',
    'export:read',
  ],
  VIEWER: [
    'metrics:read',
    'chat:read',
    'feedback:read',
  ],
};

// ============================================================================
// AUTH STORE IMPLEMENTATION
// ============================================================================

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      isLoading: false,
      user: null,
      token: null,
      refreshToken: null,
      sessionExpiry: null,
      lastActivity: Date.now(),
      requires2FA: false,
      twoFactorToken: null,
      role: null,

      // Login action
      login: (token: string, refreshToken: string, user: AdminUser) => {
        const expiry = getTokenExpiry(token);
        // Normalize user: ensure 'name' is set from 'full_name' if missing
        const normalizedUser = {
          ...user,
          name: user.name || user.full_name || '',
        };
        set({
          isAuthenticated: true,
          isLoading: false,
          user: normalizedUser,
          token,
          refreshToken,
          sessionExpiry: expiry,
          lastActivity: Date.now(),
          requires2FA: false,
          twoFactorToken: null,
          role: normalizedUser.role,
        });
      },

      // Logout action
      logout: () => {
        set({
          isAuthenticated: false,
          isLoading: false,
          user: null,
          token: null,
          refreshToken: null,
          sessionExpiry: null,
          lastActivity: Date.now(),
          requires2FA: false,
          twoFactorToken: null,
          role: null,
        });

        // Clear any cached data
        localStorage.removeItem('auth-storage');
      },

      // Set user
      setUser: (user: AdminUser) => {
        // Normalize user: ensure 'name' is set from 'full_name' if missing
        const normalizedUser = {
          ...user,
          name: user.name || user.full_name || '',
        };
        set({ user: normalizedUser });
      },

      // Set token
      setToken: (token: string) => {
        const expiry = getTokenExpiry(token);
        const userFromToken = getUserFromToken(token);

        set((state) => ({
          token,
          sessionExpiry: expiry,
          user: userFromToken ? { ...state.user, ...userFromToken } as AdminUser : state.user,
        }));
      },

      // Set refresh token
      setRefreshToken: (refreshToken: string) => {
        set({ refreshToken });
      },

      // Update last activity
      updateLastActivity: () => {
        set({ lastActivity: Date.now() });
      },

      // Set 2FA required
      set2FARequired: (required: boolean, token?: string) => {
        set({
          requires2FA: required,
          twoFactorToken: token || null,
        });
      },

      // Clear session
      clearSession: () => {
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          refreshToken: null,
          sessionExpiry: null,
          requires2FA: false,
          twoFactorToken: null,
          role: null,
        });
      },

      // Check if token is expired
      isTokenExpired: () => {
        const { token } = get();
        return !isTokenValid(token);
      },

      // Get time until token expiry
      getTimeUntilExpiry: () => {
        const { sessionExpiry } = get();
        if (!sessionExpiry) return 0;
        return Math.max(0, sessionExpiry - Date.now());
      },

      // Check if user has specific permission
      hasPermission: (permission: string) => {
        const { user } = get();
        if (!user) return false;

        const userPermissions = rolePermissions[(user.role as string).toUpperCase() as Role] || [];

        // Check for exact match
        if (userPermissions.includes(permission)) return true;

        // Check for wildcard permissions
        const [resource, action] = permission.split(':');
        const wildcardPermission = `${resource}:*`;
        if (userPermissions.includes(wildcardPermission)) return true;

        // Check for admin wildcard
        if (userPermissions.includes('admin:*')) return true;

        return false;
      },

      // Check if user has specific role
      hasRole: (role: Role) => {
        const { user } = get();
        return user?.role === role;
      },

      // Set role
      setRole: (role: Role) => {
        set((state) => ({
          role,
          user: state.user ? { ...state.user, role } : state.user,
        }));
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        sessionExpiry: state.sessionExpiry,
        lastActivity: state.lastActivity,
        role: state.role,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Check if token is still valid on rehydration
          if (state.token && !isTokenValid(state.token)) {
            state.clearSession();
          }
        }
      },
    }
  )
);