import React from 'react';
import { ChatSession } from '@/types';

interface ArchivedChatsModalProps {
  onClose: () => void;
  archivedSessions: ChatSession[];
  onRestoreSession: (sessionId: string) => void;
}

const ArchivedChatsModal: React.FC<ArchivedChatsModalProps> = ({
  onClose,
  archivedSessions,
  onRestoreSession,
}) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content max-w-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-primary-border dark:border-dark-border">
          <h3 className="text-lg font-semibold text-primary-text dark:text-dark-text">
            Archived Chats
          </h3>
          <button onClick={onClose} className="icon-btn">
            <i className="fas fa-times text-primary-text-secondary dark:text-dark-text-secondary"></i>
          </button>
        </div>

        {/* Body */}
        <div className="p-6">
          {archivedSessions.length === 0 ? (
            <div className="text-center py-8">
              <i className="fas fa-archive text-4xl text-primary-text-secondary dark:text-dark-text-secondary mb-4"></i>
              <p className="text-primary-text dark:text-dark-text mb-2">
                You don't have any archived chats yet.
              </p>
              <p className="text-primary-text-secondary dark:text-dark-text-secondary">
                When you archive a chat, it will appear here.
              </p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {archivedSessions.map((session) => (
                <div
                  key={session.id}
                  className="flex items-center justify-between p-4 bg-primary-secondary dark:bg-dark-secondary rounded-lg border border-primary-border dark:border-dark-border"
                >
                  <div className="flex-1">
                    <h4 className="text-primary-text dark:text-dark-text font-medium">
                      {session.title}
                    </h4>
                    <p className="text-sm text-primary-text-secondary dark:text-dark-text-secondary">
                      Archived on {formatDate(session.updatedAt)}
                    </p>
                    <p className="text-xs text-primary-text-secondary dark:text-dark-text-secondary">
                      {session.messages.length} messages
                    </p>
                  </div>
                  <button
                    onClick={() => onRestoreSession(session.id)}
                    className="btn-secondary ml-4"
                  >
                    <i className="fas fa-undo mr-2"></i>
                    Restore
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ArchivedChatsModal;
