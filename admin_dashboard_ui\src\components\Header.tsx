import React, { useState, useRef, useEffect } from "react";

export function Header({ onLogout }: { onLogout: () => void }) {
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  // Close dropdown on outside click
  useEffect(() => {
    function handleClick(e: MouseEvent) {
      if (ref.current && !ref.current.contains(e.target as Node)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open]);

  return (
    <header className="flex items-center justify-between px-8 py-4 border-b border-neutral-800 bg-neutral-900/80">
      <div className="text-2xl font-bold tracking-tight bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent">
        Proxima28 Admin
      </div>
      <div className="relative" ref={ref}>
        <button
          className="flex items-center gap-2 px-3 py-1 rounded bg-neutral-800 hover:bg-neutral-700 text-accent-400 font-semibold focus:outline-none"
          onClick={() => setOpen((v) => !v)}
        >
          <span>Settings</span>
          <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" strokeWidth="2" d="M6 9l6 6 6-6"/></svg>
        </button>
        {open && (
          <div className="absolute right-0 mt-2 w-48 bg-neutral-900 border border-neutral-800 rounded shadow-lg z-50">
            <div className="px-4 py-2 text-neutral-400 text-xs border-b border-neutral-800">Admin User</div>
            <button
              className="w-full text-left px-4 py-2 text-red-400 hover:bg-neutral-800 text-sm"
              onClick={() => { setOpen(false); onLogout(); }}
            >
              Logout
            </button>
          </div>
        )}
      </div>
    </header>
  );
} 