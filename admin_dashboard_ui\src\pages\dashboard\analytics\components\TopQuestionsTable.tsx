import React from "react";
import { Skeleton } from "@/components/ui/skeleton";

interface TopQuestionsTableProps {
  topQuestions: Array<{ question: string; count: number }>;
  loading?: boolean;
  error?: string | null;
}

const TopQuestionsTable: React.FC<TopQuestionsTableProps> = ({ topQuestions, loading, error }) => {
  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!topQuestions || !topQuestions.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  return (
    <div className="overflow-x-auto max-h-56">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
        <thead>
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-zinc-900 divide-y divide-gray-200 dark:divide-zinc-700">
          {topQuestions.map((q, i) => (
            <tr key={i}>
              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{q.question}</td>
              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{q.count}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TopQuestionsTable; 