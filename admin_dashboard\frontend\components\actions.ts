import {
  <PERSON><PERSON>lus,
  <PERSON>Che<PERSON>,
  MessageSquareText,
  Settings2,
} from "lucide-react";
import { ReactNode } from "react";

export type DashboardAction = {
  title: string;
  description: string;
  to: string;
  icon: ReactNode;
  gradient: string;
};

export const actions: DashboardAction[] = [
  {
    title: "Upload HR Document",
    description: "Add new HR policies, forms, or documents to the system.",
    to: "/upload_doc",
    icon: <FilePlus className="w-7 h-7 text-white group-hover:scale-110 transition-transform" />,
    gradient: "from-indigo-500 to-blue-500",
  },
  {
    title: "View Recent Queries",
    description: "Review the latest employee questions and requests.",
    to: "/queries",
    icon: <ListChecks className="w-7 h-7 text-white group-hover:scale-110 transition-transform" />,
    gradient: "from-emerald-500 to-teal-500",
  },
  {
    title: "View Chat Logs",
    description: "Monitor and audit chat conversations for compliance.",
    to: "/chat_logs",
    icon: <MessageSquareText className="w-7 h-7 text-white group-hover:scale-110 transition-transform" />,
    gradient: "from-blue-600 to-cyan-500",
  },
  {
    title: "Manage Manual Overrides",
    description: "Manually adjust or override chatbot responses.",
    to: "/overrides",
    icon: <Settings2 className="w-7 h-7 text-white group-hover:scale-110 transition-transform" />,
    gradient: "from-fuchsia-500 to-pink-500",
  },
]; 