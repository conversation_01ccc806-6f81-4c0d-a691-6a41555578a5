import { create } from "zustand";
import { persist } from "zustand/middleware";
import { ThemeConfig } from "../types";

// ============================================================================
// THEME CONFIGURATION TYPES
// ============================================================================

export type ThemeMode = "light" | "dark" | "system";
export type BorderRadius = "none" | "sm" | "md" | "lg" | "xl";
export type FontSize = "sm" | "md" | "lg";

interface ThemeState {
  // Theme configuration
  config: ThemeConfig;

  // Current resolved theme (light/dark)
  resolvedTheme: "light" | "dark";

  // System preference
  systemTheme: "light" | "dark";

  // Current theme mode (light/dark/system)
  theme: ThemeMode;

  // Actions
  setThemeMode: (mode: ThemeMode) => void;
  setPrimaryColor: (color: string) => void;
  setSecondaryColor: (color: string) => void;
  setAccentColor: (color: string) => void;
  setBorderRadius: (radius: BorderRadius) => void;
  setFontFamily: (family: string) => void;
  setFontSize: (size: FontSize) => void;
  setAnimationsEnabled: (enabled: boolean) => void;
  setHighContrast: (enabled: boolean) => void;
  setReducedMotion: (enabled: boolean) => void;
  toggleTheme: () => void;
  resetToDefaults: () => void;

  // Utilities
  getResolvedTheme: () => "light" | "dark";
  updateSystemTheme: () => void;
}

// ============================================================================
// DEFAULT THEME CONFIGURATION
// ============================================================================

const defaultThemeConfig: ThemeConfig = {
  mode: "system",
  primary_color: "#3b82f6", // Blue-500
  secondary_color: "#64748b", // Slate-500
  accent_color: "#10b981", // Emerald-500
  border_radius: "md",
  font_family: "Inter, system-ui, sans-serif",
  font_size: "md",
  animations_enabled: true,
  high_contrast: false,
  reduced_motion: false,
};

// ============================================================================
// THEME UTILITIES
// ============================================================================

const getSystemTheme = (): "light" | "dark" => {
  if (typeof window === "undefined") return "dark";
  return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
};

const resolveTheme = (mode: ThemeMode, systemTheme: "light" | "dark"): "light" | "dark" => {
  if (mode === "system") return systemTheme;
  return mode;
};

const applyThemeToDocument = (
  resolvedTheme: "light" | "dark",
  config: ThemeConfig
) => {
  const root = document.documentElement;

  // Apply theme mode
  if (resolvedTheme === "dark") {
    root.classList.add("dark");
  } else {
    root.classList.remove("dark");
  }

  // Apply custom CSS properties
  root.style.setProperty("--primary-color", config.primary_color);
  root.style.setProperty("--secondary-color", config.secondary_color);
  root.style.setProperty("--accent-color", config.accent_color);
  root.style.setProperty("--font-family", config.font_family);

  // Apply border radius
  const radiusMap = {
    none: "0px",
    sm: "0.125rem",
    md: "0.375rem",
    lg: "0.5rem",
    xl: "0.75rem",
  };
  root.style.setProperty("--border-radius", radiusMap[config.border_radius]);

  // Apply font size
  const fontSizeMap = {
    sm: "14px",
    md: "16px",
    lg: "18px",
  };
  root.style.setProperty("--base-font-size", fontSizeMap[config.font_size]);

  // Apply accessibility settings
  if (config.high_contrast) {
    root.classList.add("high-contrast");
  } else {
    root.classList.remove("high-contrast");
  }

  if (config.reduced_motion) {
    root.classList.add("reduce-motion");
  } else {
    root.classList.remove("reduce-motion");
  }

  if (!config.animations_enabled) {
    root.classList.add("no-animations");
  } else {
    root.classList.remove("no-animations");
  }
};

// ============================================================================
// THEME STORE IMPLEMENTATION
// ============================================================================

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => {
      const initialSystemTheme = getSystemTheme();
      const initialResolvedTheme = resolveTheme(defaultThemeConfig.mode, initialSystemTheme);

      return {
        config: defaultThemeConfig,
        resolvedTheme: initialResolvedTheme,
        systemTheme: initialSystemTheme,
        theme: defaultThemeConfig.mode,

        setThemeMode: (mode: ThemeMode) => {
          const { systemTheme } = get();
          const newResolvedTheme = resolveTheme(mode, systemTheme);
          const newConfig = { ...get().config, mode };

          set({
            config: newConfig,
            resolvedTheme: newResolvedTheme,
            theme: mode,
          });

          applyThemeToDocument(newResolvedTheme, newConfig);
        },

        setPrimaryColor: (color: string) => {
          const newConfig = { ...get().config, primary_color: color };
          set({ config: newConfig });
          applyThemeToDocument(get().resolvedTheme, newConfig);
        },

        setSecondaryColor: (color: string) => {
          const newConfig = { ...get().config, secondary_color: color };
          set({ config: newConfig });
          applyThemeToDocument(get().resolvedTheme, newConfig);
        },

        setAccentColor: (color: string) => {
          const newConfig = { ...get().config, accent_color: color };
          set({ config: newConfig });
          applyThemeToDocument(get().resolvedTheme, newConfig);
        },

        setBorderRadius: (radius: BorderRadius) => {
          const newConfig = { ...get().config, border_radius: radius };
          set({ config: newConfig });
          applyThemeToDocument(get().resolvedTheme, newConfig);
        },

        setFontFamily: (family: string) => {
          const newConfig = { ...get().config, font_family: family };
          set({ config: newConfig });
          applyThemeToDocument(get().resolvedTheme, newConfig);
        },

        setFontSize: (size: FontSize) => {
          const newConfig = { ...get().config, font_size: size };
          set({ config: newConfig });
          applyThemeToDocument(get().resolvedTheme, newConfig);
        },

        setAnimationsEnabled: (enabled: boolean) => {
          const newConfig = { ...get().config, animations_enabled: enabled };
          set({ config: newConfig });
          applyThemeToDocument(get().resolvedTheme, newConfig);
        },

        setHighContrast: (enabled: boolean) => {
          const newConfig = { ...get().config, high_contrast: enabled };
          set({ config: newConfig });
          applyThemeToDocument(get().resolvedTheme, newConfig);
        },

        setReducedMotion: (enabled: boolean) => {
          const newConfig = { ...get().config, reduced_motion: enabled };
          set({ config: newConfig });
          applyThemeToDocument(get().resolvedTheme, newConfig);
        },

        toggleTheme: () => {
          const { config } = get();
          const newMode: ThemeMode = config.mode === "dark" ? "light" : "dark";
          get().setThemeMode(newMode);
        },

        resetToDefaults: () => {
          const { systemTheme } = get();
          const newResolvedTheme = resolveTheme(defaultThemeConfig.mode, systemTheme);

          set({
            config: defaultThemeConfig,
            resolvedTheme: newResolvedTheme,
          });

          applyThemeToDocument(newResolvedTheme, defaultThemeConfig);
        },

        getResolvedTheme: () => get().resolvedTheme,

        updateSystemTheme: () => {
          const newSystemTheme = getSystemTheme();
          const { config } = get();
          const newResolvedTheme = resolveTheme(config.mode, newSystemTheme);

          set({
            systemTheme: newSystemTheme,
            resolvedTheme: newResolvedTheme,
          });

          if (config.mode === "system") {
            applyThemeToDocument(newResolvedTheme, config);
          }
        },
      };
    },
    {
      name: "theme-storage",
      partialize: (state) => ({
        config: state.config,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          const systemTheme = getSystemTheme();
          const resolvedTheme = resolveTheme(state.config.mode, systemTheme);

          state.systemTheme = systemTheme;
          state.resolvedTheme = resolvedTheme;

          applyThemeToDocument(resolvedTheme, state.config);
        }
      },
    }
  )
);

// ============================================================================
// SYSTEM THEME LISTENER
// ============================================================================

// Listen for system theme changes
if (typeof window !== "undefined") {
  const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

  const handleSystemThemeChange = () => {
    useThemeStore.getState().updateSystemTheme();
  };

  // Modern browsers
  if (mediaQuery.addEventListener) {
    mediaQuery.addEventListener("change", handleSystemThemeChange);
  } else {
    // Fallback for older browsers
    mediaQuery.addListener(handleSystemThemeChange);
  }
}

// ============================================================================
// THEME UTILITIES FOR COMPONENTS
// ============================================================================

export const useTheme = () => {
  const store = useThemeStore();

  return {
    config: store.config,
    resolvedTheme: store.resolvedTheme,
    systemTheme: store.systemTheme,
    isDark: store.resolvedTheme === "dark",
    isLight: store.resolvedTheme === "light",
    isSystem: store.config.mode === "system",
    theme: store.theme,

    // Actions
    setThemeMode: store.setThemeMode,
    setPrimaryColor: store.setPrimaryColor,
    setSecondaryColor: store.setSecondaryColor,
    setAccentColor: store.setAccentColor,
    setBorderRadius: store.setBorderRadius,
    setFontFamily: store.setFontFamily,
    setFontSize: store.setFontSize,
    setAnimationsEnabled: store.setAnimationsEnabled,
    setHighContrast: store.setHighContrast,
    setReducedMotion: store.setReducedMotion,
    toggleTheme: store.toggleTheme,
    resetToDefaults: store.resetToDefaults,
  };
};