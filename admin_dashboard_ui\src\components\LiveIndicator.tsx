import React from "react";
import { Wifi } from "lucide-react";

interface LiveIndicatorProps {
  className?: string;
  showIcon?: boolean;
}

export const LiveIndicator: React.FC<LiveIndicatorProps> = ({
  className = "",
  showIcon = true,
}) => {
  return (
    <div className={`flex items-center gap-1.5 ${className}`}>
      {showIcon && <Wifi className="h-3 w-3 text-green-500" />}
      <div className="flex items-center gap-1.5">
        <div className="h-2 w-2 rounded-full bg-green-500 live-indicator" />
        <span className="text-xs font-medium text-green-600 dark:text-green-400">
          Live
        </span>
      </div>
    </div>
  );
}; 