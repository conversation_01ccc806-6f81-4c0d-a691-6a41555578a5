import os
from pathlib import Path
from typing import Any, Dict, List, Union
import logging

from doctr.models import ocr_predictor
from doctr.io import DocumentFile
from PIL import Image

logger = logging.getLogger(__name__)

class OCRProcessor:
    def __init__(self):
        self.ocr_predictor = ocr_predictor(pretrained=True)

    def extract_text_with_layout(self, image_or_path: Union[str, Path, Image.Image]) -> Dict[str, Any]:
        if isinstance(image_or_path, (str, Path)):
            doc = DocumentFile.from_images(str(image_or_path))
        elif isinstance(image_or_path, Image.Image):
            doc = DocumentFile.from_images(image_or_path)
        else:
            raise ValueError("Unsupported image input type for OCR.")
        result = {
            'blocks': [],
            'tables': [],
            'raw': None
        }
        try:
            doctr_result = self.ocr_predictor(doc)
            exported = doctr_result.export()
            result['raw'] = exported
            for page in exported.get('pages', []):
                for block in page.get('blocks', []):
                    if 'lines' in block:
                        for line in block['lines']:
                            text = ' '.join([w['value'] for w in line.get('words', [])])
                            if text:
                                result['blocks'].append({'type': 'text', 'text': text})
                    if 'table' in block:
                        result['tables'].append(block['table'])
        except Exception as e:
            logger.error(f"DocTR OCR extraction failed: {e}")
            result['blocks'].append({'type': 'error', 'text': str(e)})
        return result

def test_doctr_ocr(sample_image_path: str):
    processor = OCRProcessor()
    result = processor.extract_text_with_layout(sample_image_path)
    print("\n--- Extracted Text Blocks ---")
    for block in result['blocks']:
        print(block.get('text', ''))
    if result['tables']:
        print("\n--- Extracted Tables ---")
        for table in result['tables']:
            print(table)
    print("\n--- Raw Output ---")
    print(result['raw'])

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python ocr_processor.py <image_path>")
    else:
        test_doctr_ocr(sys.argv[1]) 