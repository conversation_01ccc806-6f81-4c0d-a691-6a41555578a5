import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  X,
  FileText,
  SlidersHorizontal,
  ArrowUp,
  Mic,
  Square
} from 'lucide-react';
import { cn, formatFileSize } from '@/lib/utils';
import { useHotkeys } from 'react-hotkeys-hook';
import { FileAttachment } from '@/types';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSub, DropdownMenuSubTrigger, DropdownMenuSubContent } from '@/components/ui/dropdown-menu';
import { useSpeechRecognition } from '@/hooks/useSpeechRecognition';
// Tooltip import removed

const MinimalSlidersIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
    <line x1="4" y1="7" x2="16" y2="7" stroke="black" strokeWidth="1" strokeLinecap="round"/>
    <circle cx="8" cy="7" r="2" fill="white" stroke="black" strokeWidth="1"/>
    <line x1="4" y1="13" x2="16" y2="13" stroke="black" strokeWidth="1" strokeLinecap="round"/>
    <circle cx="12" cy="13" r="2" fill="white" stroke="black" strokeWidth="1"/>
  </svg>
);

interface ChatInputBoxProps {
  onSendMessage: (message: string, files?: FileAttachment[], responseMode?: 'concise' | 'detailed') => void;
  attachedFiles?: FileAttachment[];
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  isLoading?: boolean;
  placeholder?: string;
  mode?: 'welcome' | 'chat';
  className?: string;
  onOpenEscalation?: () => void;
  showDropdownMenu?: boolean;
  setShowDropdownMenu?: (show: boolean) => void;
  quotedText?: string;
  generatedQuestion?: string;
  onClearQuotedText?: () => void;
}

/**
 * Reusable ChatInputBox component that works in both welcome and chat modes
 */
const ChatInputBox: React.FC<ChatInputBoxProps> = ({
  onSendMessage,
  attachedFiles = [],
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  isLoading = false,
  mode = 'welcome',
  className = '',
  onOpenEscalation,
  showDropdownMenu = true,
  setShowDropdownMenu,
  quotedText,
  generatedQuestion,
  onClearQuotedText
}) => {
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [responseMode, setResponseMode] = useState<'concise' | 'detailed'>('detailed');
  const [bulbHovered, setBulbHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isVoiceRecording, setIsVoiceRecording] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Speech recognition hook
  const {
    isListening,
    isSupported: isSpeechSupported,
    transcript,
    startListening,
    stopListening,
    resetTranscript,
  } = useSpeechRecognition();

  // Keyboard shortcuts
  useHotkeys('cmd+enter,ctrl+enter', () => handleSubmit(), {
    enableOnFormTags: ['textarea'],
  });

  useHotkeys('cmd+k,ctrl+k', (e) => {
    e.preventDefault();
    fileInputRef.current?.click();
  });

  // Voice recording keyboard shortcut
  useHotkeys('cmd+shift+v,ctrl+shift+v', (e) => {
    e.preventDefault();
    handleVoiceToggle();
  });

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto first
      textarea.style.height = 'auto';
      
      // Calculate the proper height based on content
      const computedStyle = window.getComputedStyle(textarea);
      const lineHeight = parseInt(computedStyle.lineHeight) || 24;
      const paddingTop = parseInt(computedStyle.paddingTop) || 0;
      const paddingBottom = parseInt(computedStyle.paddingBottom) || 0;
      
      // Create a temporary element to measure the actual content height
      const temp = document.createElement('div');
      temp.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: ${textarea.offsetWidth}px;
        font-family: ${computedStyle.fontFamily};
        font-size: ${computedStyle.fontSize};
        line-height: ${computedStyle.lineHeight};
        padding: ${computedStyle.padding};
        border: ${computedStyle.border};
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
      `;
      temp.textContent = textarea.value || ' ';
      document.body.appendChild(temp);
      
      const contentHeight = temp.scrollHeight;
      document.body.removeChild(temp);
      
      // Set the height with a maximum limit
      const maxHeight = 200;
      const newHeight = Math.min(contentHeight, maxHeight);
      textarea.style.height = `${newHeight}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  // Prefill with generated question when available
  useEffect(() => {
    if (generatedQuestion && !message) {
      setMessage(generatedQuestion);
    }
  }, [generatedQuestion]);

  // Update message with transcript when voice recording
  useEffect(() => {
    if (isListening && transcript) {
      setMessage(transcript);
    }
  }, [transcript, isListening]);

  // Handle voice recording state changes
  useEffect(() => {
    setIsVoiceRecording(isListening);
  }, [isListening]);

  // Reset input when mode changes to welcome (New Chat)
  useEffect(() => {
    if (mode === 'welcome') {
      setMessage('');
      if (textareaRef.current) {
        (textareaRef.current as HTMLTextAreaElement).style.height = 'auto';
      }
    }
  }, [mode]);

  const handleVoiceToggle = () => {
    if (!isSpeechSupported) {
      alert('Speech recognition is not supported in your browser.');
      return;
    }

    if (isListening) {
      stopListening();
    } else {
      resetTranscript();
      setMessage('');
      startListening();
    }
  };

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    const trimmed = message.trim();
    
    // Stop voice recording if active
    if (isListening) {
      stopListening();
    }
    
    if (trimmed && !isLoading) {
      // Send only the user's question to the chatbot
      onSendMessage(trimmed, attachedFiles, responseMode);
      setMessage('');
      if (textareaRef.current) {
        (textareaRef.current as HTMLTextAreaElement).style.height = 'auto';
      }
      
      // Clear quoted text after sending
      if (onClearQuotedText) {
        onClearQuotedText();
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    files.forEach((file) => {
      const fileAttachment = {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        name: file.name,
        size: file.size,
        type: file.type,
        file: file,
      };
      onAddFile(fileAttachment);
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRaiseConcern = () => {
    if (typeof onOpenEscalation === 'function') onOpenEscalation();
  };

  const canSend = message.trim().length > 0 && !isLoading;

  const containerVariants = {
    focused: {
      boxShadow: "0 0 0 2px hsl(var(--ring))",
      transition: { duration: 0.2 }
    },
    unfocused: {
      boxShadow: "0 0 0 0px hsl(var(--ring))",
      transition: { duration: 0.2 }
    }
  };

  const placeholderText = mode === 'welcome'
    ? 'Ask anything about company policies, benefits, or procedures…'
    : 'Ask your next question or explore more HR topics…';

  // Waveform animation for recording state
  const WaveformAnimation = () => (
    <div className="flex items-center space-x-0.5">
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="w-0.5 bg-white rounded-full"
          animate={{
            height: [4, 12, 4],
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: i * 0.1,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );

  return (
    <div className={cn("w-full flex flex-col items-center", className)}>
      {/* File Attachments */}
      {attachedFiles.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="mb-3 flex flex-wrap gap-2 w-full"
        >
          {attachedFiles.map((file) => (
            <motion.div
              key={file.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="flex items-center gap-2 rounded-lg bg-gray-100 dark:bg-gray-700 px-3 py-2 text-sm"
            >
              <FileText className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              <span className="font-medium text-gray-900 dark:text-gray-100">{file.name}</span>
              <span className="text-gray-500 dark:text-gray-400">
                ({formatFileSize(file.size)})
              </span>
              <button
                onClick={() => onRemoveFile(file.id)}
                className="h-5 w-5 rounded hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-400 transition-colors flex items-center justify-center"
              >
                <X className="h-3 w-3" />
              </button>
            </motion.div>
          ))}
        </motion.div>
      )}

      {/* Quoted Text Display */}
      <AnimatePresence>
        {quotedText && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-3 p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                  🧩 Regarding:
                </div>
                <div className="text-sm text-gray-700 dark:text-gray-300 italic">
                  "{quotedText}"
                </div>
              </div>
              <button
                type="button"
                onClick={onClearQuotedText}
                className="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                title="Remove quote"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Input Layout */}
      <motion.form
        onSubmit={handleSubmit}
        variants={containerVariants}
        animate={isFocused ? "focused" : "unfocused"}
        className={cn(
          "relative w-full bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700",
          "shadow-lg hover:shadow-xl transition-all duration-200",
          // No focus-within or active border color
          mode === 'welcome' && "shadow-xl",
          isVoiceRecording && "ring-2 ring-purple-400/20 border-purple-400"
        )}
      >


        {/* Text Input Area - Large input taking most space */}
        <div className="px-2 pt-2 pb-1">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
            placeholder={isVoiceRecording ? "Listening..." : placeholderText}
            className={cn(
              "w-full bg-transparent border-none outline-none resize-none",
              "text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",
              "text-base leading-6 max-h-32 overflow-y-auto px-2 min-h-[19.9px] !min-h-[19.9px]",
              mode === 'welcome' ? "py-[0.6rem] !py-[0.6rem]" : "py-[calc(0.6rem-3mm)] !py-[calc(0.6rem-3mm)]",
              isVoiceRecording && "placeholder-purple-400"
            )}
            rows={1}
            disabled={isLoading}
          />
        </div>

        {/* Icons Row Below - Separate row with all functional icons */}
        <div className="flex items-center justify-between px-2 pb-2 pt-1">
          {/* Left Icons */}
          <div className="flex items-center gap-x-[5.8px] ml-1.5">
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="w-[34.3px] h-[34.3px] flex items-center justify-center rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Attach file"
            >
              <Plus className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </button>
            {showDropdownMenu && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <div style={{ position: 'relative', display: 'inline-block' }}>
                    <button
                      type="button"
                      className="w-[34.3px] h-[34.3px] flex items-center justify-center rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      title="Suggestions"
                      onMouseEnter={() => setBulbHovered(true)}
                      onMouseLeave={() => setBulbHovered(false)}
                    >
                      <MinimalSlidersIcon />
                    </button>
                    {bulbHovered && (
                      <span style={{
                        position: 'absolute',
                        top: '110%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        background: 'white',
                        color: '#333',
                        padding: '2px 8px',
                        borderRadius: 4,
                        fontSize: 12,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                        zIndex: 10,
                        whiteSpace: 'nowrap',
                        border: '1px solid #eee',
                      }}>
                        {`Response Mode: ${responseMode.charAt(0).toUpperCase() + responseMode.slice(1)}`}
                      </span>
                    )}
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="pr-0">
                  <DropdownMenuSub>
                    <button type="button" onClick={handleRaiseConcern} className="px-4 pt-2 pb-1 text-xs font-semibold text-red-500 hover:underline focus:outline-none w-full text-left">Raise a Concern</button>
                    <DropdownMenuSubTrigger className="px-4 py-2 text-xs font-semibold text-gray-500 cursor-pointer">Response Style</DropdownMenuSubTrigger>
                    <DropdownMenuSubContent className="pr-0">
                      <DropdownMenuItem onClick={() => setResponseMode('concise')}>
                        Concise {responseMode === 'concise' && <span className="ml-1 inline-block align-middle" style={{width:6.8,height:6.8,borderRadius:'50%',background:'green'}}></span>}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setResponseMode('detailed')}>
                        Detailed {responseMode === 'detailed' && <span className="ml-1 inline-block align-middle" style={{width:6.8,height:6.8,borderRadius:'50%',background:'green'}}></span>}
                      </DropdownMenuItem>
                    </DropdownMenuSubContent>
                  </DropdownMenuSub>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>

          {/* Right Icons - Only functional ones */}
          <div className="flex items-center gap-x-[5.8px] mr-1.5">
            {/* Voice Button with Animation */}
            <motion.button
              type="button"
              onClick={handleVoiceToggle}
              className={cn(
                "w-[34.3px] h-[34.3px] flex items-center justify-center rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200 relative",
                isVoiceRecording 
                  ? "bg-purple-400 hover:bg-purple-500 text-white border-purple-400" 
                  : "hover:bg-gray-100 dark:hover:bg-gray-700"
              )}
              title={isVoiceRecording ? "Stop recording (Ctrl+Shift+V)" : "Voice input (Ctrl+Shift+V)"}
            >
              <AnimatePresence mode="wait">
                {isVoiceRecording ? (
                  <motion.div
                    key="stop"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="flex items-center justify-center"
                  >
                    <WaveformAnimation />
                  </motion.div>
                ) : (
                  <motion.div
                    key="mic"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="flex items-center justify-center"
                  >
                    <svg 
                      className="w-4 h-4 text-gray-500 dark:text-gray-400" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    >
                      {/* Classic microphone design */}
                      <path d="M12 1a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3Z"/>
                      <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                      <line x1="12" y1="19" x2="12" y2="23"/>
                      <line x1="8" y1="23" x2="16" y2="23"/>
                    </svg>
                  </motion.div>
                )}
              </AnimatePresence>
              

            </motion.button>

            {/* Send Button - Teal colored like reference */}
            <button
              type="submit"
              disabled={!canSend || isVoiceRecording}
              className={cn(
                "w-[34.3px] h-[34.3px] flex items-center justify-center rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200",
                canSend && !isVoiceRecording
                  ? "bg-black hover:bg-black text-white shadow-sm"
                  : "bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed"
              )}
              title={isVoiceRecording ? "Stop recording first" : "Send message"}
            >
              {isLoading ? (
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <ArrowUp className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.txt,.md,.csv,.xls,.xlsx,.ppt,.pptx,.html,.htm,.json,.xml,.rtf,.odt,.ods,.odp,.png,.jpg,.jpeg,.gif,.bmp,.tiff,.webp"
          onChange={handleFileUpload}
          className="hidden"
        />
      </motion.form>
    </div>
  );
};

export default ChatInputBox;
