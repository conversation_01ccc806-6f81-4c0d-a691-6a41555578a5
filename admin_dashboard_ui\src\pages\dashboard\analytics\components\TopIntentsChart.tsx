import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "recharts";

const data = [
  { intent: "Leave Balance", count: 120, color: "#FFB020" },
  { intent: "Payslip", count: 95, color: "#38BDF8" },
  { intent: "Attendance", count: 80, color: "#34D399" },
  { intent: "Policy", count: 60, color: "#6366f1" },
  { intent: "Holiday", count: 45, color: "#f59e42" },
];

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border border-border rounded-lg shadow-lg p-3">
        <p className="text-sm font-medium text-popover-foreground">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm text-muted-foreground">
            <span 
              className="inline-block w-3 h-3 rounded mr-2" 
              style={{ backgroundColor: entry.color }}
            />
            Count: <span className="font-medium">{entry.value}</span>
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const TopIntentsChart = () => (
  <div className="h-64 w-full">
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={data}
        layout="vertical"
        margin={{ top: 16, right: 16, left: 16, bottom: 0 }}
      >
        <CartesianGrid 
          strokeDasharray="3 3" 
          stroke="hsl(var(--muted-foreground))" 
          opacity={0.3}
          horizontal={false}
        />
        <XAxis 
          type="number" 
          stroke="hsl(var(--muted-foreground))" 
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        <YAxis 
          dataKey="intent" 
          type="category" 
          stroke="hsl(var(--muted-foreground))" 
          width={120} 
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend 
          verticalAlign="top" 
          height={36}
          wrapperStyle={{
            fontSize: '12px',
            color: 'hsl(var(--muted-foreground))'
          }}
        />
        <Bar 
          dataKey="count" 
          fill="hsl(var(--chart-success))" 
          name="Count" 
          radius={[0, 4, 4, 0]} 
          opacity={0.8}
        />
      </BarChart>
    </ResponsiveContainer>
  </div>
);

export default TopIntentsChart; 