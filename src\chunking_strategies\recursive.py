from .base import ChunkingStrategy, Chunking<PERSON>onfig, TokenizerManager
from typing import List
import logging

class RecursiveStrategy(ChunkingStrategy):
    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        try:
            from langchain.text_splitter import RecursiveCharacterTextSplitter, TokenTextSplitter
            if config.use_tokens:
                splitter = TokenTextSplitter(
                    chunk_size=config.max_tokens,
                    chunk_overlap=config.token_overlap
                )
            else:
                splitter = RecursiveCharacterTextSplitter(
                    chunk_size=config.chunk_size,
                    chunk_overlap=config.chunk_overlap,
                    separators=["\n\n", "\n", ". ", " ", ""]
                )
            return splitter.split_text(text)
        except Exception as e:
            logging.warning(f"LangChain splitter failed: {e}")
        return self._manual_recursive_split(text, config, tokenizer_manager)

    def _manual_recursive_split(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        separators = ["\n\n", "\n", ". ", "! ", "? ", "; ", ": ", ", ", " "]
        def split_with_separator(text: str, sep: str) -> List[str]:
            if not sep:
                return [text]
            return [chunk.strip() for chunk in text.split(sep) if chunk.strip()]
        def recursive_split(text: str, sep_index: int = 0) -> List[str]:
            if sep_index >= len(separators):
                return [text] if text.strip() else []
            current_sep = separators[sep_index]
            parts = split_with_separator(text, current_sep)
            chunks = []
            current_chunk = ""
            for part in parts:
                test_chunk = f"{current_chunk}{current_sep}{part}" if current_chunk else part
                size_metric = (tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name) 
                               if config.use_tokens else len(test_chunk))
                size_limit = config.max_tokens if config.use_tokens else config.chunk_size
                if size_metric <= size_limit:
                    current_chunk = test_chunk
                else:
                    if current_chunk:
                        chunks.append(current_chunk)
                    if size_metric > size_limit:
                        chunks.extend(recursive_split(part, sep_index + 1))
                        current_chunk = ""
                    else:
                        current_chunk = part
            if current_chunk:
                chunks.append(current_chunk)
            return chunks
        return recursive_split(text)

    def get_name(self) -> str:
        return "recursive" 