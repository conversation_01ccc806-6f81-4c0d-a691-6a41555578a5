import React, { createContext, useContext, useEffect, useState } from 'react';
import { Roles } from '../types';
import { useAuthStore } from '../hooks/useAuthStore';

interface PermissionsContextType {
  role: string;
  roleLevel: number;
  permissions: string[];
  loading: boolean;
}

export const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

export const PermissionsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [role, setRole] = useState('');
  const [roleLevel, setRoleLevel] = useState(0);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const token = useAuthStore((state) => state.token); // Get JWT token from store

  useEffect(() => {
    async function fetchPermissions() {
      setLoading(true);
      try {
        if (!token) {
          // No token: do not set any role or permissions
          setRole('');
          setRoleLevel(0);
          setPermissions([]);
          setLoading(false);
          return;
        }
        // Use real endpoint only
        const res = await fetch('/api/admin-users/me', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!res.ok) throw new Error('Failed to fetch user info');
        const data = await res.json();
        setRole(data.role);
        setRoleLevel(data.role_level || data.roleLevel || 0);
        setPermissions(data.permissions || []);
      } catch (e) {
        // On error, clear role and permissions
        setRole('');
        setRoleLevel(0);
        setPermissions([]);
      } finally {
        setLoading(false);
      }
    }
    fetchPermissions();
  }, [token]);

  return (
    <PermissionsContext.Provider value={{ role, roleLevel, permissions, loading }}>
      {children}
    </PermissionsContext.Provider>
  );
}; 