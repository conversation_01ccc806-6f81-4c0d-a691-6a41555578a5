export interface SelectedTextInfo {
  text: string;
  range: Range;
  element: HTMLElement;
}

export interface SmartPrompt {
  id: string;
  text: string;
  icon: string;
  description: string;
}

export const getSelectedText = (): SelectedTextInfo | null => {
  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) {
    return null;
  }

  const range = selection.getRangeAt(0);
  const text = selection.toString().trim();

  if (!text) {
    return null;
  }

  return {
    text,
    range,
    element: range.commonAncestorContainer.parentElement as HTMLElement
  };
};

export const generateSmartPrompts = (selectedText: string): SmartPrompt[] => {
  const prompts: SmartPrompt[] = [
    {
      id: 'explain',
      text: `Explain "${selectedText}" in more detail`,
      icon: '💡',
      description: 'Get a detailed explanation'
    },
    {
      id: 'clarify',
      text: `What does "${selectedText}" mean?`,
      icon: '❓',
      description: 'Clarify the meaning'
    },
    {
      id: 'examples',
      text: `Give me examples related to "${selectedText}"`,
      icon: '📝',
      description: 'See practical examples'
    },
    {
      id: 'implications',
      text: `What are the implications of "${selectedText}"?`,
      icon: '🔍',
      description: 'Understand the consequences'
    }
  ];

  // Filter prompts based on text length and content
  if (selectedText.length < 10) {
    return prompts.slice(0, 2); // Only show explain and clarify for short text
  }

  return prompts;
};

export const createQuestionFromSelection = (selectedText: string): string => {
  // Create a question based on the selected text
  return `Can you explain more about: "${selectedText}"`;
};

export const clearSelection = (): void => {
  const selection = window.getSelection();
  if (selection) {
    selection.removeAllRanges();
  }
};