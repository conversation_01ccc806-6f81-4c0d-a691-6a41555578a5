import React from "react";
import { Link } from "react-router-dom";
import type { DashboardAction } from "./actions";

const DashboardCard: React.FC<DashboardAction> = ({
  title,
  description,
  to,
  icon,
  gradient,
}) => (
  <Link
    to={to}
    className={`
      group bg-gradient-to-br ${gradient}
      rounded-2xl shadow-xl p-6 flex flex-col gap-4
      transition-transform transform hover:-translate-y-1 hover:scale-105
      hover:shadow-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400
      dark:bg-gradient-to-br dark:from-slate-800 dark:to-slate-900
      text-white
    `}
    style={{ textDecoration: "none" }}
  >
    <div className="flex items-center gap-4">
      <div className="p-3 bg-slate-900/70 rounded-xl shadow-inner">
        {icon}
      </div>
      <div>
        <h2 className="text-xl font-bold mb-1">{title}</h2>
        <p className="text-slate-200 text-sm">{description}</p>
      </div>
    </div>
  </Link>
);

export default DashboardCard; 