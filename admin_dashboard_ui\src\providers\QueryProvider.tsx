import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import toast from 'react-hot-toast';

// ============================================================================
// QUERY CLIENT CONFIGURATION
// ============================================================================

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Global query defaults
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      refetchOnMount: true,
    },
    mutations: {
      // Global mutation defaults
      retry: false,
      onError: (error: any) => {
        // Global error handling for mutations
        const message = error?.response?.data?.message || error?.message || 'An error occurred';
        console.error('Mutation error:', error);
        
        // Don't show toast for specific errors that are handled locally
        const skipToastErrors = ['validation_error', 'unauthorized', 'forbidden'];
        const errorType = error?.response?.data?.type;
        
        if (!skipToastErrors.includes(errorType)) {
          toast.error(message);
        }
      },
    },
  },
});

// ============================================================================
// QUERY CLIENT EVENT LISTENERS
// ============================================================================

// Listen for query errors

// Listen for mutation events

// ============================================================================
// QUERY PROVIDER COMPONENT
// ============================================================================

interface QueryProviderProps {
  children: React.ReactNode;
}

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* Show React Query DevTools in development */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false}
          position="bottom"
        />
      )}
    </QueryClientProvider>
  );
};

// ============================================================================
// QUERY CLIENT UTILITIES
// ============================================================================

// Export query client for use in utilities and middleware
export { queryClient };

// Utility function to prefetch data
export const prefetchQuery = async (queryKey: any[], queryFn: () => Promise<any>) => {
  await queryClient.prefetchQuery({
    queryKey,
    queryFn,
  });
};

// Utility function to set query data
export const setQueryData = (queryKey: any[], data: any) => {
  queryClient.setQueryData(queryKey, data);
};

// Utility function to invalidate queries
export const invalidateQueries = (queryKey?: any[]) => {
  if (queryKey) {
    queryClient.invalidateQueries({ queryKey });
  } else {
    queryClient.invalidateQueries();
  }
};

// Utility function to remove queries
export const removeQueries = (queryKey: any[]) => {
  queryClient.removeQueries({ queryKey });
};

// Utility function to get cached data
export const getQueryData = (queryKey: any[]) => {
  return queryClient.getQueryData(queryKey);
};

// ============================================================================
// OFFLINE SUPPORT
// ============================================================================

// Handle online/offline status
if (typeof window !== 'undefined') {
  window.addEventListener('online', () => {
    queryClient.resumePausedMutations();
    queryClient.invalidateQueries();
  });
  
  window.addEventListener('offline', () => {
    // Queries will be paused automatically
    toast.error('You are offline. Some features may not work properly.');
  });
}

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

// Monitor query performance in development
if (process.env.NODE_ENV === 'development') {
  queryClient.getQueryCache().subscribe((event) => {
    if (event?.type === 'updated') {
      const query = event.query;
      const state = query.state;
      // Log slow queries (> 2 seconds)
      // Only use dataUpdatedAt for timing
      if (state.dataUpdatedAt && state.dataUpdatedAt > 0) {
        // Example: log if dataUpdatedAt is more than 2 seconds ago
        const now = Date.now();
        if (now - state.dataUpdatedAt > 2000) {
          console.warn('Slow query detected:', {
            queryKey: query.queryKey,
            duration: now - state.dataUpdatedAt,
          });
        }
      }
    }
  });
}

// ============================================================================
// CACHE PERSISTENCE (Optional)
// ============================================================================

// You can add cache persistence here if needed
// import { persistQueryClient } from '@tanstack/react-query-persist-client-core'
// import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister'

// const localStoragePersister = createSyncStoragePersister({
//   storage: window.localStorage,
// })

// persistQueryClient({
//   queryClient,
//   persister: localStoragePersister,
// })

export default QueryProvider;
