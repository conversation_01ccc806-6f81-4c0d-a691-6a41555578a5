{"common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "warning": "Avertissement", "info": "Information", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "import": "Importer", "refresh": "Actualiser", "close": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yes": "O<PERSON>", "no": "Non", "confirm": "Confirmer", "back": "Retour", "next": "Suivant", "previous": "Précédent", "submit": "So<PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "clear": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON>ut", "none": "Aucun", "actions": "Actions", "status": "Statut", "active": "Actif", "inactive": "Inactif", "enabled": "Activé", "disabled": "Désactivé", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "pending": "En attente", "completed": "<PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "processing": "En cours"}, "navigation": {"dashboard": "Tableau de bord", "analytics": "Analytiques", "overview": "<PERSON><PERSON><PERSON><PERSON>", "insights": "Insights", "chat_logs": "Journaux de chat", "feedback": "Commentaires et escalades", "trends": "Tendances", "escalated": "<PERSON><PERSON><PERSON><PERSON>", "resolution": "Résolution", "ai_insights": "Insights IA", "weekly_digest": "Résumé <PERSON>", "policy_drift": "Dérive des politiques", "training_tools": "Outils de formation", "misunderstood": "Requêtes mal comprises", "ner_intent": "NER et intention", "live_support": "Support en direct", "queue": "File d'attente", "ongoing": "En cours", "compliance": "Conformité et audit", "gdpr": "RGPD", "deletion": "Suppression de données", "sensitive": "<PERSON><PERSON><PERSON> sensibles", "users": "Utilisateurs", "admins": "Administrateurs", "roles": "<PERSON><PERSON><PERSON>", "settings": "Paramètres", "theme": "Thème", "email": "Email", "device_intelligence": "Intelligence des appareils", "feature_requests": "Demandes de fonctionnalités", "api_management": "Gestion API", "webhooks": "Webhooks", "system_health": "<PERSON><PERSON> du système"}, "auth": {"login": "Connexion", "logout": "Déconnexion", "email": "Email", "password": "Mot de passe", "otp": "Mot de passe à usage unique", "two_factor": "Authentification à deux facteurs", "backup_codes": "Codes de sauvegarde", "remember_me": "Se souvenir de moi", "forgot_password": "Mot de passe oublié ?", "reset_password": "Réinitialiser le mot de passe", "change_password": "Changer le mot de passe", "current_password": "Mot de passe actuel", "new_password": "Nouveau mot de passe", "confirm_password": "Confirmer le mot de passe", "enable_2fa": "Activer l'authentification à deux facteurs", "disable_2fa": "Désactiver l'authentification à deux facteurs", "scan_qr_code": "Scanner le code QR avec votre application d'authentification", "enter_verification_code": "Entrer le code de vérification", "invalid_credentials": "Identifiants invalides", "session_expired": "Session expirée. Veuillez vous reconnecter.", "access_denied": "<PERSON><PERSON>ès refusé", "login_success": "Connexion réussie", "logout_success": "Déconnexion réussie", "password_changed": "Mot de passe changé avec succès", "2fa_enabled": "Authentification à deux facteurs activée", "2fa_disabled": "Authentification à deux facteurs désactivée"}, "dashboard": {"title": "Tableau de bord Admin Proxima28", "welcome": "Bon retour", "overview": "<PERSON><PERSON><PERSON><PERSON>", "quick_stats": "Statistiques rapides", "recent_activity": "Activité récente", "system_status": "État du système", "alerts": "<PERSON><PERSON><PERSON>", "notifications": "Notifications", "total_users": "Total des utilisateurs", "active_sessions": "Sessions actives", "total_queries": "Total des requêtes", "avg_response_time": "Temps de réponse moyen", "user_satisfaction": "Satisfaction utilisateur", "escalation_rate": "Taux d'escalade", "resolution_rate": "Taux de résolution", "uptime": "Temps de fonctionnement", "last_updated": "Dernière mise à jour", "refresh_data": "Actualiser les données", "view_details": "Voir les détails", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "loading_metrics": "Chargement des métriques...", "error_loading": "Erreur lors du chargement des données"}, "metrics": {"chatbot_performance": "Performance du chatbot", "user_engagement": "Engagement utilisateur", "sentiment_analysis": "Analyse de sentiment", "intent_recognition": "Reconnaissance d'intention", "response_times": "Temps de réponse", "error_rates": "<PERSON><PERSON> d'erreur", "anomalies_detected": "Anomalies détectées", "performance_trends": "Tendances de performance", "daily_queries": "Re<PERSON><PERSON><PERSON><PERSON> quotidiennes", "weekly_summary": "Résumé <PERSON>", "monthly_report": "Rapport mensuel", "real_time_metrics": "Métriques en temps réel", "historical_data": "Données historiques", "comparison": "Comparaison", "benchmark": "Référence", "target": "Objectif", "actual": "<PERSON><PERSON><PERSON>", "variance": "<PERSON><PERSON><PERSON>", "improvement": "Amélioration", "decline": "D<PERSON><PERSON>lin"}, "chat": {"sessions": "Sessions de chat", "messages": "Messages", "duration": "<PERSON><PERSON><PERSON>", "satisfaction": "Satisfaction", "escalate": "<PERSON><PERSON><PERSON><PERSON>", "resolve": "<PERSON><PERSON><PERSON><PERSON>", "add_note": "Ajouter une note", "view_transcript": "Voir la transcription", "user_info": "Informations utilisateur", "session_details": "<PERSON><PERSON><PERSON> de la session", "escalation_reason": "Raison de l'escalade", "resolution_notes": "Notes de résolution", "chat_history": "Historique des chats", "search_conversations": "Rechercher des conversations", "filter_by_date": "Filtrer par date", "filter_by_status": "Filtrer par statut", "export_transcript": "Exporter la transcription", "escalated_chats": "Chats escaladés", "resolved_chats": "<PERSON><PERSON> résolus", "ongoing_chats": "Chats en cours", "average_duration": "<PERSON><PERSON><PERSON> moyenne", "total_messages": "Total des messages"}, "users": {"user_management": "Gestion des utilisateurs", "admin_users": "Utilisateurs administrateurs", "create_user": "C<PERSON>er un utilisateur", "edit_user": "Modifier l'utilisateur", "delete_user": "Supprimer l'utilisateur", "invite_user": "Inviter un utilisateur", "user_details": "Détails de l'utilisateur", "full_name": "Nom complet", "email_address": "<PERSON><PERSON><PERSON> email", "role": "R<PERSON><PERSON>", "organization": "Organisation", "last_login": "Dernière connexion", "created_at": "<PERSON><PERSON><PERSON>", "updated_at": "Mis à jour le", "user_status": "Statut utilisateur", "permissions": "Permissions", "change_role": "<PERSON><PERSON> le <PERSON>", "reset_password": "Réinitialiser le mot de passe", "send_invitation": "Envoyer l'invitation", "revoke_access": "Révoquer l'accès", "user_activity": "Activité utilisateur", "login_history": "Historique de connexion", "device_info": "Informations sur l'appareil", "security_settings": "Paramètres de sécurité"}, "roles": {"superadmin": "Super Administrateur", "admin": "Administrateur", "support_agent": "Agent de support", "compliance_auditor": "Auditeur de conformité", "developer": "Développeur", "hr_lead": "Responsable RH", "viewer": "Visualiseur", "role_description": "Description du rôle", "permissions_included": "Permissions incluses", "manage_roles": "<PERSON><PERSON><PERSON> rôles", "create_role": "<PERSON><PERSON>er un rôle", "edit_role": "Modifier le rôle", "delete_role": "<PERSON><PERSON><PERSON><PERSON> le rôle", "assign_role": "Attribuer un rôle", "role_hierarchy": "Hiérarchie des rôles", "inherited_permissions": "Permissions héritées"}, "errors": {"generic_error": "Une erreur s'est produite. Veuillez réessayer.", "network_error": "Erreur réseau. Veuillez vérifier votre connexion.", "server_error": "Erreur serveur. Veuillez réessayer plus tard.", "validation_error": "Veuillez vérifier votre saisie et réessayer.", "permission_denied": "Vous n'avez pas la permission d'effectuer cette action.", "not_found": "La ressource demandée n'a pas été trouvée.", "timeout_error": "<PERSON><PERSON><PERSON> d'attente dépassé. Veuillez réessayer.", "rate_limit_exceeded": "Limite de taux dépassée. Veuillez réessayer plus tard.", "maintenance_mode": "Le système est en maintenance. Veuillez réessayer plus tard.", "session_expired": "Votre session a expiré. Veuillez vous reconnecter.", "invalid_token": "Token invalide ou expiré.", "file_too_large": "La taille du fichier dépasse la limite maximale.", "unsupported_format": "Format de fichier non supporté.", "quota_exceeded": "Quota dépassé. Veuillez mettre à niveau votre plan."}, "success": {"data_saved": "Données enregistrées avec succès", "user_created": "Utilisateur c<PERSON>é avec succès", "user_updated": "Utilisateur mis à jour avec succès", "user_deleted": "Utilisateur supprimé avec succès", "role_changed": "<PERSON><PERSON><PERSON> changé avec succès", "settings_updated": "Paramètres mis à jour avec succès", "export_completed": "Export terminé avec succès", "import_completed": "Import terminé avec succès", "email_sent": "Email envoy<PERSON> avec succès", "notification_sent": "Notification envoyée avec succès", "backup_created": "Sauvegarde c<PERSON>ée avec succès", "cache_cleared": "<PERSON><PERSON> vidé avec succès", "system_updated": "Système mis à jour avec succès"}}