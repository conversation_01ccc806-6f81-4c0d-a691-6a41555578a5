import json
import os
import re
import threading
import time
import hashlib
from pathlib import Path
from typing import Dict, Any, Tuple, List, Optional
from datetime import datetime # Added for timestamp

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, DataLoader
from transformers import AutoModelForSequenceClassification, AutoTokenizer
from sklearn.preprocessing import LabelEncoder
from sklearn.utils import resample
from sklearn.model_selection import train_test_split
from sklearn.calibration import calibration_curve
from sklearn.metrics import brier_score_loss, log_loss, accuracy_score
from rapidfuzz import fuzz
from scipy.stats import entropy
from scipy.optimize import minimize_scalar

from ..utils.logger import get_logger
from ..config import DATA_DIR

import joblib
import traceback

# Limit CPU usage to 2 threads
torch.set_num_threads(2)

logger = get_logger(__name__)

# Now you can use the / operator for path concatenation
INTENTS_CONFIG_PATH = DATA_DIR / "config" / "intents.json"
TRAINING_DATA_PATH = DATA_DIR / "training" / "intent_training_data.jsonl"
MODEL_DIR = DATA_DIR / "models" / "intent_classifier" # Updated as requested
MODEL_PATH = MODEL_DIR / "model.safetensors"  # Changed to HuggingFace format
CONFIG_PATH = MODEL_DIR / "config.json"  # Added for HuggingFace model
ENCODER_PATH = MODEL_DIR / "label_encoder.joblib"
VERSION_PATH = MODEL_DIR / "version.txt"
CALIBRATION_PATH = DATA_DIR / "calibration" / "intent_calibration_stats.json" # Updated as requested

# You might also want to ensure these directories exist before using them
MODEL_DIR.mkdir(parents=True, exist_ok=True)
(DATA_DIR / "config").mkdir(parents=True, exist_ok=True)
(DATA_DIR / "training").mkdir(parents=True, exist_ok=True)
(DATA_DIR / "calibration").mkdir(parents=True, exist_ok=True)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

def convert_numpy_to_python_types(obj):
    """
    Recursively converts NumPy types (np.float32, np.int64, np.bool_, np.ndarray, np.nan)
    within a dictionary or list to native Python types (float, int, bool, list, None)
    for JSON serialization.
    """
    if isinstance(obj, np.float32) or isinstance(obj, np.float64):
        return float(obj)
    elif isinstance(obj, np.int32) or isinstance(obj, np.int64):
        return int(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (float, np.number)) and np.isnan(obj):
        return None  # Convert NaN to None for JSON compatibility
    elif isinstance(obj, dict):
        return {k: convert_numpy_to_python_types(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_to_python_types(elem) for elem in obj]
    else:
        return obj


class AutoTemperatureCalibrator:
    """
    Performs temperature scaling for model calibration.
    """
    def __init__(self, model_output_dim: int):
        self.temperature = nn.Parameter(torch.ones(1).to(device))
        self.model_output_dim = model_output_dim

    def apply_temperature(self, logits: torch.Tensor, temperature_value: float = None) -> torch.Tensor:
        """Applies the temperature scaling to the logits."""
        if temperature_value is None:
            temperature_value = self.temperature.item()
        return logits / temperature_value

    def _loss_function(self, temperature_param: np.ndarray, logits: np.ndarray, labels: np.ndarray) -> float:
        """Loss function for temperature optimization (Negative Log Likelihood)."""
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        temp_tensor = torch.tensor(temperature_param, device=device, dtype=torch.float32)
        calibrated_logits = torch.from_numpy(logits).to(device) / temp_tensor
        loss = nn.CrossEntropyLoss()(calibrated_logits, torch.from_numpy(labels).to(device))
        return loss.item()

    def calibrate(self, logits: torch.Tensor, labels: torch.Tensor) -> Tuple[float, Dict[str, Any]]:
        """
        Calibrates the model using temperature scaling on a validation set.
        Returns the optimal temperature and calibration metrics.
        """
        if logits.numel() == 0 or labels.numel() == 0:
            logger.warning("No data for calibration. Returning default temperature and empty metrics.")
            return 1.0, {
                "method": "N/A - No Data",
                "expected_calibration_error": None,
                "negative_log_likelihood": None,
                "accuracy": None,
                "data_size": 0,
                "timestamp": datetime.now().isoformat()
            }

        logits_np = logits.detach().cpu().numpy()
        labels_np = labels.detach().cpu().numpy()

        initial_temperature = 1.0

        res = minimize_scalar(
            self._loss_function,
            bounds=(0.01, 100.0),
            args=(logits_np, labels_np),
            method='bounded'
        )

        best_temperature = res.x.item()
        self.temperature.data = torch.ones(1).to(device) * best_temperature

        calibrated_probs = self.apply_temperature(logits, best_temperature).softmax(dim=1)

        ece = self._calculate_ece(calibrated_probs, labels)
        nll = self._calculate_nll(calibrated_probs, labels)
        accuracy = self._calculate_accuracy(calibrated_probs, labels)
        
        calibration_metrics = {
            "method": "temperature_scaling",
            "expected_calibration_error": ece,
            "negative_log_likelihood": nll,
            "accuracy": accuracy,
            "data_size": len(labels_np),
            "timestamp": datetime.now().isoformat()
        }

        # Safe formatting for floats or None
        def fmt(val):
            return f"{val:.4f}" if isinstance(val, (float, int)) and val is not None else "N/A"
        logger.info(f"Calibration finished. Optimal Temperature: {fmt(best_temperature)}, ECE: {fmt(ece)}, NLL: {fmt(nll)}, Accuracy: {fmt(accuracy)}")
        
        return best_temperature, calibration_metrics

    def _calculate_ece(self, calibrated_probs: torch.Tensor, labels: torch.Tensor, n_bins: int = 10) -> Optional[float]:
        """Calculates Expected Calibration Error (ECE)."""
        if labels.numel() == 0:
            return None
        
        confidences = calibrated_probs.max(dim=1).values
        predictions = calibrated_probs.argmax(dim=1)
        
        ece_sum = 0.0
        bin_boundaries = torch.linspace(0, 1, n_bins + 1).to(device)
        
        for i in range(n_bins):
            lower_bound = bin_boundaries[i]
            upper_bound = bin_boundaries[i+1]
            
            bin_indices = (confidences >= lower_bound) & (confidences < upper_bound)
            
            if bin_indices.sum() > 0:
                avg_confidence = confidences[bin_indices].mean().item()
                correct_predictions_in_bin = (predictions[bin_indices] == labels[bin_indices]).float().mean().item()
                
                ece_sum += abs(correct_predictions_in_bin - avg_confidence) * (bin_indices.sum().item() / labels.numel())
            
        return ece_sum if not np.isnan(ece_sum) else None

    def _calculate_nll(self, calibrated_probs: torch.Tensor, labels: torch.Tensor) -> Optional[float]:
        """Calculates Negative Log Likelihood (NLL)."""
        if labels.numel() == 0:
            return None
        
        try:
            # Removed 'eps' parameter as it's deprecated/removed in newer scikit-learn versions
            nll = log_loss(labels.cpu().numpy(), calibrated_probs.cpu().numpy())
            return nll if not np.isnan(nll) else None
        except ValueError as e:
            logger.warning(f"Error calculating NLL: {e}. Returning None.")
            return None

    def _calculate_accuracy(self, calibrated_probs: torch.Tensor, labels: torch.Tensor) -> Optional[float]:
        """Calculates accuracy."""
        if labels.numel() == 0:
            return None
            
        predicted_labels = torch.argmax(calibrated_probs, dim=1)
        accuracy = (predicted_labels == labels).float().mean().item()
        return accuracy if not np.isnan(accuracy) else None

class IntentClassifier(nn.Module):
    """
    A robust intent classifier built on Sentence-BERT, with auto-calibration and
    dynamic retraining capabilities. Now with production-grade enhancements.
    """
    _model_loaded = False
    _health_status = {'ready': False, 'last_error': None}
    _version_history = []

    def __init__(self, auto_calibrate: bool = True, retrain_on_new_data: bool = True):
        super().__init__()
        logger.info(f"Using unified model: data/models_cache/bge-base-en-v1.5")

        # Set up device handling
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Intent classifier using device: {self.device}")

        self.model = None  # HuggingFace model
        self.tokenizer = None
        self.classifier_head = None
        self.label_encoder = LabelEncoder()
        self.intents: Dict[str, Any] = self._load_intents_config()
        self.auto_calibrator = None
        self.calibration_info = {'temperature': 1.0, 'calibration_metrics': {}, 'calibration_history': []}
        self._model_lock = threading.Lock()  # Instance-level lock for thread safety
        self._training_lock = threading.Lock()
        self.training_data = self._load_training_data()
        self._new_samples_count = 0
        self._retrain_batch_size = 10
        self.retrain_on_new_data = retrain_on_new_data
        self.auto_calibrate = auto_calibrate
        self._load_or_train_model(lazy=True)

    def _validate_query(self, query):
        if not isinstance(query, str) or not query.strip():
            raise ValueError("Query must be a non-empty string.")
        return query.strip()

    def _validate_intent(self, intent):
        if not isinstance(intent, str) or not intent.strip():
            raise ValueError("Intent must be a non-empty string.")
        return intent.strip()

    def _sanitize_path(self, path):
        # Only allow paths within DATA_DIR
        p = Path(path).resolve()
        if not str(p).startswith(str(DATA_DIR.resolve())):
            raise ValueError("Invalid path access attempt.")
        return p

    def _load_intents_config(self) -> Dict[str, Any]:
        """Loads intent configuration from JSON."""
        if not INTENTS_CONFIG_PATH.exists():
            logger.warning(f"Intents config file not found: {INTENTS_CONFIG_PATH}. Using default.")
            return {
                "leave": {"keywords": ["leave", "vacation", "time off", "sick", "absence", "pto"]},
                "benefits": {"keywords": ["benefits", "insurance", "health", "dental", "vision"]},
                "compensation": {"keywords": ["salary", "pay", "compensation", "bonus", "raise"]},
                "onboarding": {"keywords": ["onboarding", "orientation", "new hire", "training"]},
                "policy": {"keywords": ["policy", "guidelines", "rules", "procedures"]},
                "offboarding": {"keywords": ["offboarding", "exit", "termination", "resignation"]},
                "greeting": {"keywords": ["hello", "hi", "hey", "dude", "good morning", "good evening", "greetings"]},
                "general": {"keywords": ["general", "other", "miscellaneous"]},
                "irrelevant": {"keywords": []}
            }
        
        with open(INTENTS_CONFIG_PATH, "r", encoding="utf-8") as f:
            return json.load(f)

    def _load_training_data(self) -> List[Dict[str, str]]:
        """Loads training data from a JSONL file."""
        data = []
        if TRAINING_DATA_PATH.exists():
            with open(TRAINING_DATA_PATH, "r", encoding="utf-8") as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:  # Skip empty lines
                        continue
                    try:
                        data.append(json.loads(line))
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON decode error on line {line_num}: {e}. Skipping line: {line[:100]}...")
                        continue
        return data

    def _save_training_data(self):
        """Saves current training data to the JSONL file."""
        TRAINING_DATA_PATH.parent.mkdir(parents=True, exist_ok=True)
        with open(TRAINING_DATA_PATH, "w", encoding="utf-8") as f:
            for sample in self.training_data:
                f.write(json.dumps(sample) + "\n")

    def _load_calibration_metrics(self) -> Dict[str, Any]:
        """Load saved calibration metrics"""
        if CALIBRATION_PATH.exists():
            try:
                with open(CALIBRATION_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to load calibration metrics (JSONDecodeError): {e}. File might be empty or corrupt. Returning empty dict.")
                return {}
            except Exception as e:
                logger.warning(f"Failed to load calibration metrics: {e}. Returning empty dict.")
                return {}
        return {}

    def _save_calibration_metrics(self):
        """Save the current calibration temperature and metrics to file."""
        try:
            CALIBRATION_PATH.parent.mkdir(parents=True, exist_ok=True)
            serializable_info = convert_numpy_to_python_types(self.calibration_info)
            with open(CALIBRATION_PATH, "w", encoding="utf-8") as f:
                json.dump(serializable_info, f, indent=4)
            logger.info(f"Calibration metrics saved to {CALIBRATION_PATH}")
        except Exception as e:
            logger.warning(f"Failed to save calibration metrics: {e}")

    def _generate_synthetic_data(self) -> List[Dict[str, str]]:
        """Generates synthetic training data based on keywords."""
        synthetic_data = []
        for intent, config in self.intents.items():
            if "keywords" in config:
                for keyword in config["keywords"]:
                    synthetic_data.append({"query": f"I need info about {keyword}", "intent": intent})
                    synthetic_data.append({"query": f"What's the policy on {keyword}?", "intent": intent})
        return synthetic_data

    def _load_hf_model_and_tokenizer(self):
        if self.model is None or self.tokenizer is None:
            # Check if we have a trained HuggingFace model
            if MODEL_PATH.exists() and CONFIG_PATH.exists():
                logger.info(f"Loading trained HuggingFace model from {MODEL_DIR}")
                self.model = AutoModelForSequenceClassification.from_pretrained(str(MODEL_DIR))
                self.tokenizer = AutoTokenizer.from_pretrained(str(MODEL_DIR))
                self.model = self.model.to(self.device)
            else:
                logger.info("No trained model found, using base model")
                self.model = AutoModelForSequenceClassification.from_pretrained("data/models_cache/bge-base-en-v1.5")
                self.tokenizer = AutoTokenizer.from_pretrained("data/models_cache/bge-base-en-v1.5")
                self.model = self.model.to(self.device)
            self.model.eval()

    def _embed(self, texts: list) -> torch.Tensor:
        self._load_hf_model_and_tokenizer()
        
        # Process in batches to avoid memory issues
        batch_size = 32  # Further reduced batch size for memory efficiency
        all_embeddings = []
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        logger.info(f"Starting embedding generation for {len(texts)} texts in {total_batches} batches of size {batch_size}")
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            logger.info(f"Processing embedding batch {batch_num}/{total_batches} ({len(batch_texts)} texts)")
            
            # Tokenize with very conservative max_length to avoid warnings
            encoded_input = self.tokenizer(
                batch_texts, padding=True, truncation=True, return_tensors='pt', max_length=128
            ).to(device)
            
            with torch.no_grad():
                model_output = self.model(**encoded_input)
            
            # Handle different model output types
            if hasattr(model_output, 'last_hidden_state'):
                cls_embeddings = model_output.last_hidden_state[:, 0]
            elif hasattr(model_output, 'hidden_states') and model_output.hidden_states:
                cls_embeddings = model_output.hidden_states[-1][:, 0]
            else:
                # For models that return logits directly, use the input embeddings
                cls_embeddings = model_output.logits[:, 0] if hasattr(model_output, 'logits') else encoded_input['input_ids'][:, 0]
            
            # Ensure cls_embeddings is 2D for normalization
            if cls_embeddings.dim() == 1:
                cls_embeddings = cls_embeddings.unsqueeze(0)
            
            normed = torch.nn.functional.normalize(cls_embeddings, p=2, dim=1)
            all_embeddings.append(normed)
            
            # Clear GPU memory
            del encoded_input, model_output, cls_embeddings
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        logger.info("Embedding generation complete!")
        # Concatenate all batches
        return torch.cat(all_embeddings, dim=0)

    def _prepare_data(self, queries: List[str], intents: List[str]):
        """Prepares data for training: encodes labels and embeds queries."""
        if not queries or not intents:
            raise ValueError("Queries or intents list cannot be empty for data preparation.")

        # Ensure LabelEncoder is fitted on all *possible* intents (from config)
        # AND all *actual* intents present in the current batch of training data.
        unique_intents_in_data = list(set(intents))
        all_possible_and_actual_intents = sorted(list(set(list(self.intents.keys()) + unique_intents_in_data)))
        self.label_encoder.fit(all_possible_and_actual_intents)
        labels = self.label_encoder.transform(intents)
        # Prefix queries for e5
        queries = [f"query: {q}" for q in queries]
        embeddings = self._embed(queries)
        return embeddings, torch.tensor(labels, dtype=torch.long, device=self.device)

    def _train_model(self):
        """Trains the intent classification model."""
        with self._training_lock:
            logger.info("Starting model training...")
            all_data = self.training_data + self._generate_synthetic_data()
            if not all_data:
                logger.warning("No training data available. Skipping model training.")
                return
            
            # Use all available training data for better model performance
            logger.info(f"Using all {len(all_data)} training samples for optimal model performance")
            
            logger.info(f"Total training samples: {len(all_data)}")
            queries = [d["query"] for d in all_data]
            intents = [d["intent"] for d in all_data]
            
            logger.info("Preparing training data (embedding generation)...")
            try:
                embeddings, labels = self._prepare_data(queries, intents)
                logger.info(f"Data preparation complete. Embeddings shape: {embeddings.shape}")
            except ValueError as e:
                logger.warning(f"Data preparation failed: {e}. Skipping training.")
                return
            # Check class distribution and handle insufficient samples
            unique_labels, counts = np.unique(labels.cpu().numpy(), return_counts=True)
            min_samples_per_class = counts.min()
            
            logger.info(f"Class distribution: {dict(zip(unique_labels, counts))}")
            
            if len(labels) < 2 or len(unique_labels) < 2:
                logger.warning(f"Not enough data for training. Total samples: {len(labels)}, Classes: {len(unique_labels)}. Using full data for training.")
                X_train, X_val = embeddings.cpu().numpy(), embeddings.cpu().numpy()
                y_train, y_val = labels.cpu().numpy(), labels.cpu().numpy()
            elif min_samples_per_class < 2:
                logger.warning(f"Some classes have insufficient samples (min: {min_samples_per_class}). Filtering out classes with < 2 samples.")
                
                # Get original intent names for valid classes
                original_intent_names = self.label_encoder.inverse_transform(unique_labels)
                valid_class_indices = unique_labels[counts >= 2]
                valid_intent_names = self.label_encoder.inverse_transform(valid_class_indices)
                
                if len(valid_intent_names) < 2:
                    logger.warning("After filtering, less than 2 classes remain. Using non-stratified split on all data.")
                    X_train, X_val, y_train, y_val = train_test_split(
                        embeddings.cpu().numpy(), labels.cpu().numpy(), test_size=0.2, random_state=42
                    )
                else:
                    logger.info(f"Using {len(valid_intent_names)} classes with sufficient samples for stratified split")
                    
                    # Filter data to only include valid classes
                    valid_indices = np.isin(labels.cpu().numpy(), valid_class_indices)
                    filtered_embeddings = embeddings[valid_indices]
                    filtered_labels = labels[valid_indices]
                    
                    # Create new label encoder with only valid intent names
                    from sklearn.preprocessing import LabelEncoder
                    new_encoder = LabelEncoder()
                    new_encoder.fit(valid_intent_names)  # Fit with actual intent names
                    filtered_labels_encoded = new_encoder.transform(
                        self.label_encoder.inverse_transform(filtered_labels.cpu().numpy())
                    )
                    
                    X_train, X_val, y_train, y_val = train_test_split(
                        filtered_embeddings.cpu().numpy(), filtered_labels_encoded, test_size=0.2, random_state=42, stratify=filtered_labels_encoded
                    )
                    
                    # Update label encoder to only include valid classes with proper names
                    self.label_encoder = new_encoder
            else:
                logger.info(f"Using stratified split with {len(unique_labels)} classes")
                X_train, X_val, y_train, y_val = train_test_split(
                    embeddings.cpu().numpy(), labels.cpu().numpy(), test_size=0.2, random_state=42, stratify=labels.cpu().numpy()
                )
            X_train, X_val = torch.tensor(X_train, device=self.device), torch.tensor(X_val, device=self.device)
            y_train, y_val = torch.tensor(y_train, device=self.device), torch.tensor(y_val, device=self.device)
            train_dataset = TensorDataset(X_train, y_train)
            train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True, drop_last=True)
            # Get embedding dimension from model config
            input_dim = self.model.config.hidden_size
            output_dim = len(self.label_encoder.classes_)
            logger.info(f"Model architecture: input_dim={input_dim}, output_dim={output_dim} (classes: {list(self.label_encoder.classes_)})")
            
            # Better model architecture with dropout for regularization
            self.classifier_head = nn.Sequential(
                nn.Dropout(0.3),
                nn.Linear(input_dim, input_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(input_dim // 2, output_dim)
            ).to(device)
            # Ensure auto_calibrator is initialized
            self.auto_calibrator = AutoTemperatureCalibrator(output_dim)
            # Optimized training parameters for better accuracy
            optimizer = torch.optim.AdamW(self.classifier_head.parameters(), lr=5e-5, weight_decay=0.01)
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=50, eta_min=1e-6)
            criterion = nn.CrossEntropyLoss()
            num_epochs = 50  # Much more epochs for better accuracy
            logger.info(f"Starting training for {num_epochs} epochs...")
            
            # Early stopping variables
            best_val_loss = float('inf')
            patience = 10
            patience_counter = 0
            
            for epoch in range(num_epochs):
                self.classifier_head.train()
                epoch_loss = 0.0
                batch_count = 0
                for batch_embeddings, batch_labels in train_loader:
                    optimizer.zero_grad()
                    outputs = self.classifier_head(batch_embeddings)
                    loss = criterion(outputs, batch_labels)
                    loss.backward()
                    
                    # Gradient clipping to prevent exploding gradients
                    torch.nn.utils.clip_grad_norm_(self.classifier_head.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    epoch_loss += loss.item()
                    batch_count += 1
                avg_loss = epoch_loss / batch_count
                
                # Validation step every 5 epochs
                if (epoch + 1) % 5 == 0:
                    self.classifier_head.eval()
                    with torch.no_grad():
                        val_outputs = self.classifier_head(X_val)
                        val_loss = criterion(val_outputs, y_val)
                        val_preds = torch.argmax(val_outputs, dim=1)
                        val_accuracy = (val_preds == y_val).float().mean().item()
                        logger.info(f"Epoch {epoch+1}/{num_epochs}, Train Loss: {avg_loss:.4f}, Val Loss: {val_loss:.4f}, Val Accuracy: {val_accuracy:.4f}")
                        
                        # Early stopping check
                        if val_loss < best_val_loss:
                            best_val_loss = val_loss
                            patience_counter = 0
                            # Save best model
                            torch.save(self.classifier_head.state_dict(), MODEL_PATH)
                            logger.info(f"New best model saved! Val Loss: {val_loss:.4f}, Val Accuracy: {val_accuracy:.4f}")
                        else:
                            patience_counter += 1
                            logger.info(f"No improvement for {patience_counter} validation checks")
                        
                        # Early stopping
                        if patience_counter >= patience:
                            logger.info(f"Early stopping triggered after {epoch+1} epochs")
                            break
                    self.classifier_head.train()
                else:
                    logger.info(f"Epoch {epoch+1}/{num_epochs}, Average Loss: {avg_loss:.4f}")
                
                # Step the scheduler
                scheduler.step()
            MODEL_DIR.mkdir(parents=True, exist_ok=True)
            torch.save(self.classifier_head.state_dict(), MODEL_PATH)
            logger.info(f"Model saved to {MODEL_PATH}")
            import joblib
            joblib.dump(self.label_encoder, ENCODER_PATH)
            logger.info(f"Label encoder saved to {ENCODER_PATH}")
            with open(VERSION_PATH, "w") as f:
                f.write(hashlib.md5(str(time.time()).encode()).hexdigest())
            logger.info("Model training complete.")
            self.classifier_head.eval()
            with torch.no_grad():
                val_logits = self.classifier_head(X_val)
            # Always run calibration after training
            temperature, calibration_metrics = self.auto_calibrator.calibrate(val_logits, y_val)
            self.calibration_info["temperature"] = temperature
            self.calibration_info["auto_calibrated"] = True
            self.calibration_info["calibration_metrics"] = calibration_metrics
            self.calibration_info["calibration_history"].append({
                "timestamp": datetime.now().isoformat(),
                "temperature": temperature,
                "method": calibration_metrics.get("method", "temperature_scaling"),
                "expected_calibration_error": calibration_metrics.get("expected_calibration_error"),
                "negative_log_likelihood": calibration_metrics.get("negative_log_likelihood"),
                "accuracy": calibration_metrics.get("accuracy"),
                "model_version": self._get_model_version()
            })
            self._save_calibration_metrics()

    def _load_or_train_model(self, lazy=False):
        with self._model_lock:
            try:
                if not self._model_loaded and not lazy:
                    self._load_hf_model_and_tokenizer()
                    
                    # Check if we have a trained model and label encoder
                    if MODEL_PATH.exists() and CONFIG_PATH.exists() and ENCODER_PATH.exists():
                        logger.info(f"Loading trained HuggingFace model and label encoder")
                        loaded_encoder = joblib.load(ENCODER_PATH)
                        
                        # Handle case where encoder might be loaded as dict
                        if isinstance(loaded_encoder, dict):
                            logger.warning("Label encoder loaded as dict, creating new LabelEncoder")
                            from sklearn.preprocessing import LabelEncoder
                            import json
                            mapping_path = MODEL_DIR / "intent_mapping.json"
                            if mapping_path.exists():
                                with open(mapping_path, 'r') as f:
                                    intent_mapping = json.load(f)
                                self.label_encoder = LabelEncoder()
                                intents = [intent_mapping[str(i)] for i in range(len(intent_mapping))]
                                self.label_encoder.fit(intents)
                            else:
                                raise ValueError("intent_mapping.json not found")
                        else:
                            self.label_encoder = loaded_encoder
                        
                        # Load the trained HuggingFace model
                        self.model = AutoModelForSequenceClassification.from_pretrained(str(MODEL_DIR))
                        self.tokenizer = AutoTokenizer.from_pretrained(str(MODEL_DIR))
                        self.model.to(device)
                        self.model.eval()
                        
                        # For HuggingFace models, the classifier is already part of the model
                        # No need for separate classifier_head
                        self.classifier_head = None
                        
                        # Use model's built-in label mapping instead of separate label encoder
                        self.label_encoder = None
                        self.model_labels = list(self.model.config.id2label.values())
                        
                        # Ensure auto_calibrator is initialized
                        output_dim = len(self.model_labels)
                        self.auto_calibrator = AutoTemperatureCalibrator(output_dim)
                        logger.info(f"HuggingFace model loaded successfully with {len(self.model_labels)} labels")
                        
                        # Only perform auto-calibration if metrics are missing or outdated
                        need_calibration = True
                        if CALIBRATION_PATH.exists():
                            cal_time = CALIBRATION_PATH.stat().st_mtime
                            model_time = MODEL_PATH.stat().st_mtime if MODEL_PATH.exists() else 0
                            encoder_time = ENCODER_PATH.stat().st_mtime if ENCODER_PATH.exists() else 0
                            # If calibration is newer than both model and encoder, skip
                            if cal_time > model_time and cal_time > encoder_time:
                                logger.info("Calibration metrics are up-to-date. Skipping auto-calibration.")
                                need_calibration = False
                        if need_calibration:
                            self._perform_auto_calibration(recalibrate=True)
                    else:
                        logger.warning(f"Trained model or label encoder not found. Retraining model.")
                        self._train_model()
                    
                    self._model_loaded = True
                    self._health_status['ready'] = True
                    self._health_status['last_error'] = None
                    self._version_history.append(self._get_model_version())
                elif lazy:
                    # For lazy loading, just mark as ready without loading the model
                    logger.info("Lazy loading mode - model will be loaded on first use")
                    self._model_loaded = False
                    self._health_status['ready'] = False
                    self._health_status['last_error'] = None
            except Exception as e:
                logger.error(f"Model loading failed: {e}\n{traceback.format_exc()}")
                self._health_status['ready'] = False
                self._health_status['last_error'] = str(e)

    def _get_model_version(self) -> str:
        """Retrieves the current model version."""
        if VERSION_PATH.exists():
            with open(VERSION_PATH, "r") as f:
                return f.read().strip()
        return "N/A"

    def _perform_auto_calibration(self, recalibrate: bool = False):
        """
        Performs or re-applies auto-calibration using temperature scaling.
        This method must be defined before it is called in __init__.
        """
        # For HuggingFace models, we don't have a separate classifier_head
        # The classification is done directly by the model
        if self.classifier_head is None and not hasattr(self, 'model'):
            logger.warning("Model not trained/loaded, cannot perform calibration.")
            return
        
        # Skip calibration for HuggingFace models since they don't need it
        if self.classifier_head is None:
            logger.info("Skipping calibration for HuggingFace model (not needed)")
            return

        if not recalibrate and self.calibration_info["auto_calibrated"]:
            logger.info("Auto-calibration already performed and not set to recalibrate. Skipping.")
            return

        logger.info("Performing auto-calibration...")

        # Prepare a small validation set for calibration if not already done during training
        # If _train_model just ran, it already used a validation set.
        # If loading an existing model, we need to generate one or use saved data.
        # For simplicity, if recalibrate is True, we'll re-run calibration on a subset of current training data.
        all_data = self.training_data + self._generate_synthetic_data()
        if not all_data:
            logger.warning("No data for calibration. Skipping auto-calibration.")
            return

        # Use a small, fresh subset for validation for calibration,
        # ensuring it's from the same distribution as training data
        queries = [d["query"] for d in all_data]
        intents = [d["intent"] for d in all_data]

        try:
            embeddings, labels = self._prepare_data(queries, intents)
        except ValueError as e:
            logger.warning(f"Data preparation for calibration failed: {e}. Skipping calibration.")
            return

        if len(labels) < 2 or len(set(labels.tolist())) < 2:
            logger.warning("Not enough data or classes for calibration split. Skipping auto-calibration.")
            return
        
        # Take a subset if the data is too large, or if not enough for split, use full data for calibration
        if len(labels) > 100: # Example: Use up to 100 samples for validation
            _, X_val_cal, _, y_val_cal = train_test_split(
                embeddings.cpu().numpy(), labels.cpu().numpy(), test_size=min(0.2, 100/len(labels)), random_state=42, stratify=labels.cpu().numpy()
            )
        else:
            X_val_cal, y_val_cal = embeddings.cpu().numpy(), labels.cpu().numpy()

        X_val_cal = torch.tensor(X_val_cal, device=self.device)
        y_val_cal = torch.tensor(y_val_cal, device=self.device)

        self.classifier_head.eval()
        with torch.no_grad():
            val_logits = self.classifier_head(X_val_cal)

        temperature, calibration_metrics = self.auto_calibrator.calibrate(val_logits, y_val_cal)

        self.calibration_info["temperature"] = temperature
        self.calibration_info["auto_calibrated"] = True
        self.calibration_info["calibration_metrics"] = calibration_metrics
        
        # Append to history, or if it's the very first calibration, this is already the current metrics
        # Avoid duplicate history entries if _train_model also triggers this.
        # This check is simplified for now.
        if not self.calibration_info["calibration_history"] or \
           self.calibration_info["calibration_history"][-1].get("timestamp") != calibration_metrics.get("timestamp"):
            self.calibration_info["calibration_history"].append({
                "timestamp": datetime.now().isoformat(), # Use current time for history
                "temperature": temperature,
                "method": calibration_metrics.get("method", "temperature_scaling"),
                "expected_calibration_error": calibration_metrics.get("expected_calibration_error"),
                "negative_log_likelihood": calibration_metrics.get("negative_log_likelihood"),
                "accuracy": calibration_metrics.get("accuracy"),
                "model_version": self._get_model_version()
            })

        self._save_calibration_metrics()
        logger.debug(f"DEBUG: Calibration metrics assigned in _perform_auto_calibration: {self.calibration_info['calibration_metrics']}")


    def update_training_data(self, query: str, intent: str):
        """Adds new training samples and triggers retraining if threshold is met."""
        self.training_data.append({"query": query, "intent": intent})
        self._save_training_data()
        self._new_samples_count += 1
        logger.info(f"Appended training data: {{'query': '{query}', 'intent': '{intent}'}}")
        
        if self.retrain_on_new_data and self._new_samples_count >= self._retrain_batch_size:
            logger.info(f"Retraining triggered due to {self._new_samples_count} new samples...")
            retrain_thread = threading.Thread(target=self._train_model)
            retrain_thread.start()
            self._new_samples_count = 0

    def get_calibration_info(self) -> Dict[str, Any]:
        """Retrieves current calibration temperature and metrics."""
        return self.calibration_info

    def classify_intent(self, query: str) -> Dict[str, Any]:
        try:
            query = self._validate_query(query)
            # Extraction intent keyword override
            extraction_keywords = ["extract", "full text", "full content", "get document", "show complete text"]
            if any(kw in query.lower() for kw in extraction_keywords):
                return {
                    "intent": "text_extraction",
                    "confidence": 1.0,
                    "query": query,
                    "keywords": extraction_keywords,
                    "calibration_info": getattr(self, 'calibration_info', {}),
                    "error": None
                }
            with self._model_lock:
                if self.model is None or not hasattr(self.label_encoder, 'classes_'):
                    self._load_or_train_model(lazy=True)
                if self.model is None or not hasattr(self.label_encoder, 'classes_'):
                    return {"intent": "unknown", "confidence": 0.0, "error": "Model not ready", "query": query}
                
                self.model.eval()
                
                # For HuggingFace models, we use the model directly
                if self.classifier_head is None:
                    # Use the HuggingFace model directly
                    # Ensure model is on the correct device (avoid checking model.device as it might hang)
                    self.model = self.model.to(self.device)

                    encoded_input = self.tokenizer(
                        [f"query: {query}"],
                        padding=True,
                        truncation=True,
                        return_tensors='pt',
                        max_length=128
                    )

                    # Move input tensors to the same device as the model
                    input_ids = encoded_input['input_ids'].to(self.device)
                    attention_mask = encoded_input['attention_mask'].to(self.device)

                    with torch.no_grad():
                        logger.debug(f"About to call model with input_ids shape: {input_ids.shape}, attention_mask shape: {attention_mask.shape}")
                        try:
                            outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
                            logger.debug(f"Model forward pass completed successfully")
                            logits = outputs.logits
                            logger.debug(f"Logits shape: {logits.shape}")
                        except Exception as e:
                            logger.error(f"Model forward pass failed: {e}")
                            raise
                else:
                    # Fallback to old method if classifier_head exists
                    query_embedding = self._embed([f"query: {query}"])
                    logits = self.classifier_head(query_embedding)
                
                # Apply temperature scaling if calibration info exists and auto_calibrator is available
                if (hasattr(self, 'calibration_info') and self.calibration_info.get("temperature") 
                    and self.auto_calibrator is not None):
                    calibrated_logits = self.auto_calibrator.apply_temperature(logits, self.calibration_info["temperature"])
                else:
                    calibrated_logits = logits
                probabilities = torch.softmax(calibrated_logits, dim=1)
                confidence, predicted_index = torch.max(probabilities, dim=1)
                
                # Use model's built-in label mapping
                if hasattr(self, 'model_labels') and self.model_labels:
                    predicted_intent = self.model_labels[predicted_index.item()]
                    logger.debug(f"DEBUG: Predicted index: {predicted_index.item()}, Decoded intent: {predicted_intent}, Available classes: {self.model_labels}")
                else:
                    # Fallback to label encoder if available
                    predicted_intent = self.label_encoder.inverse_transform(predicted_index.cpu().numpy())[0]
                    logger.debug(f"DEBUG: Predicted index: {predicted_index.item()}, Decoded intent: {predicted_intent}, Available classes: {list(self.label_encoder.classes_)}")
                
                # Ensure predicted_intent is a string
                if not isinstance(predicted_intent, str):
                    logger.warning(f"Predicted intent is not a string: {predicted_intent} (type: {type(predicted_intent)})")
                    predicted_intent = str(predicted_intent)
                
                # Log available intents for debugging
                logger.debug(f"Available intents in config: {list(self.intents.keys()) if self.intents else 'No intents config'}")
                matched_keywords = []
                if predicted_intent in self.intents and "keywords" in self.intents[predicted_intent]:
                    for keyword in self.intents[predicted_intent]["keywords"]:
                        if fuzz.partial_ratio(keyword.lower(), query.lower()) > 80:
                            matched_keywords.append(keyword)
                if predicted_intent == "irrelevant":
                    num_classes = len(self.model_labels) if hasattr(self, 'model_labels') else len(self.label_encoder.classes_)
                    normalized_entropy = entropy(probabilities.detach().cpu().numpy()[0]) / np.log(num_classes)
                    confidence_score = 1.0 - normalized_entropy
                else:
                    confidence_score = confidence.item()
                return {
                    "intent": predicted_intent,
                    "confidence": confidence_score,
                    "query": query,
                    "keywords": list(set(matched_keywords)),
                    "calibration_info": self.calibration_info.copy(),
                    "error": None
                }
        except Exception as e:
            logger.error(f"Intent classification failed: {e}\n{traceback.format_exc()}")
            return {"intent": "unknown", "confidence": 0.0, "error": str(e), "query": query}

    def classify_batch(self, queries: list) -> list:
        results = []
        for q in queries:
            try:
                results.append(self.classify_intent(q))
            except Exception as e:
                results.append({"intent": "unknown", "confidence": 0.0, "error": str(e), "query": q})
        return results

    def health_check(self):
        """Returns model readiness and last error."""
        return self._health_status.copy()

    def log_resource_usage(self):
        usage = {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'available_memory_mb': psutil.virtual_memory().available // 1024 // 1024
        }
        logger.info(f"Resource usage: {usage}")
        return usage

    def get_version_history(self):
        return list(self._version_history)

    # Placeholder for monitoring hook
    def monitoring_hook(self, event, details=None):
        logger.info(f"MONITORING EVENT: {event} | Details: {details}")

    # Placeholder for access control
    def check_access(self, user):
        # Implement real access control in API layer
        return True

    def save_as_joblib(self, joblib_path: str = None):
        """
        Save the current model as a joblib file for easier deployment.
        
        Args:
            joblib_path (str): Path to save the joblib file. If None, uses default path.
        """
        if joblib_path is None:
            joblib_path = str(MODEL_DIR / "intent_classifier.joblib")
        
        # Ensure model is loaded
        if not self._model_loaded:
            self._load_or_train_model(lazy=False)
        
        # Create model package
        model_package = {
            'model': self.model,
            'tokenizer': self.tokenizer,
            'label_encoder': self.label_encoder,
            'intents_config': self.intents,
            'model_info': {
                'accuracy': 0.9839,  # From your training results
                'confidence_threshold': 0.8,
                'max_length': 512,
                'model_name': 'BAAI/bge-base-en-v1.5',
                'version': self._get_model_version(),
                'calibration_info': self.calibration_info
            }
        }
        
        # Save as joblib
        import joblib
        joblib.dump(model_package, joblib_path)
        
        logger.info(f"✅ Model saved as joblib: {joblib_path}")
        logger.info(f"📦 Package includes:")
        logger.info(f"   - Trained model")
        logger.info(f"   - Tokenizer")
        logger.info(f"   - Label encoder")
        logger.info(f"   - Intents config")
        logger.info(f"   - Model info")
        
        return joblib_path

    def load_from_joblib(self, joblib_path: str):
        """
        Load model from a joblib file.
        
        Args:
            joblib_path (str): Path to the joblib file
        """
        import joblib
        
        # Load complete model package
        model_package = joblib.load(joblib_path)
        
        # Extract components
        self.model = model_package['model']
        self.tokenizer = model_package['tokenizer']
        self.label_encoder = model_package['label_encoder']
        self.intents = model_package['intents_config']
        self.model_info = model_package['model_info']
        
        # Set model to evaluation mode
        self.model.eval()
        self.model = self.model.to(self.device)
        
        # Update calibration info if available
        if 'calibration_info' in model_package['model_info']:
            self.calibration_info = model_package['model_info']['calibration_info']
        
        logger.info(f"✅ Intent classifier loaded from joblib: {joblib_path}")
        logger.info(f"📊 Model accuracy: {self.model_info.get('accuracy', 'N/A')}")
        logger.info(f"🎯 Confidence threshold: {self.model_info.get('confidence_threshold', 'N/A')}")
        
        self._model_loaded = True
        self._health_status['ready'] = True
        self._health_status['last_error'] = None

    # Example usage and error handling
    @staticmethod
    def example_usage():
        clf = IntentClassifier()
        print(clf.classify_intent("How do I apply for leave?"))
        print(clf.classify_batch(["How do I apply for leave?", "What is the dress code?"]))
        print(clf.health_check())
        print(clf.log_resource_usage())

if __name__ == "__main__":
    print("Initializing IntentClassifier...")
    classifier = IntentClassifier(auto_calibrate=True)
    
    print("\n=== Training Model First ===")
    # Force training by calling _load_or_train_model with lazy=False
    classifier._load_or_train_model(lazy=False)
    print("Model training completed!")
    
    print("\n=== Initial Classification Results with Auto-Calibration ===")

    test_queries = [
        # Leave related queries
        "How do I apply for vacation leave?",
        "What is the process for requesting sick leave?",
        "Can I take unpaid time off next month?",
        "I need to request a vacation day.",
        
        # Benefits related queries
        "What are the benefits of our health insurance plan?",
        "Does the company offer dental coverage?",
        
        # Compensation related queries
        "How is my salary determined?",
        "What is the process for a raise?",
        
        # Policy related queries
        "What is the company's policy on remote work?",
        "Are there guidelines for workplace safety?",
        
        # Onboarding related queries
        "What is the onboarding process for new hires?",
        "How do I complete my new hire paperwork?",
        
        # Offboarding related queries
        "What is the process for resigning from the company?",
        "How do I schedule an exit interview?",
        
        # Greeting related queries
        "Hello, how can I assist you today?",
        "Hi there, what can I do for you?",
        
        # General queries
        "What are the office hours?",
        "Where can I find the HR contact information?",
        
        # Irrelevant queries
        "asdf jkl; qwerty zxcvb",
        "blargle fliptop monkey cheese",
        
        # Salary discrepancy queries
        "My salary is low this month",
        "I got less pay than expected",
        "There's a CTC mismatch in my payslip",
        "My net pay is wrong",
        "I didn't get my full salary",
        "My basic pay has been reduced",
        "The deduction looks wrong",
        "There's a salary cut issue",
        "My in-hand salary is low",
        "This is not my actual pay",
        "What is this deduction for?",
        "There's a payroll error",
        "There's a pay mismatch",
        "Where is the rest of my salary?",
        
        # Missing bonus queries
        "My bonus was not credited",
        "I didn't get my bonus",
        "Bonus is missing from my payslip",
        "I'm still waiting for my bonus",
        "My bonus is not added to salary",
        "My bonus is gone",
        "I expected bonus but didn't receive it",
        "Where's my bonus?",
        "Bonus is not processed",
        "There's a bonus issue",
        "Bonus was supposed to be this month",
        "No bonus received",
        "Bonus not shown in statement",
        
        # Tax deduction queries
        "TDS is too high",
        "Extra tax was deducted",
        "Tax is not calculated properly",
        "Wrong TDS amount",
        "Why is my tax so much?",
        "Deduction under 80C is missing",
        "Form 16 doesn't match",
        "Section 80C investment not considered",
        "LIC not counted in tax",
        "Wrong tax bracket applied",
        "Taxable income issue",
        "Excess tax this month",
        "TDS slab problem",
        
        # Variable pay queries
        "Variable pay is missing",
        "Incentive not received",
        "Quarterly performance pay not given",
        "My targets are achieved but no variable pay",
        "Incentive payout delay",
        "Performance bonus issue",
        "My variable amount is low",
        "No incentive shown",
        "Incentive not reflected",
        "Variable pay not credited",
        
        # Delayed bonus queries
        "Bonus delay",
        "Bonus pushed to next cycle",
        "Bonus stuck",
        "HR said bonus is late",
        "Why bonus still not credited?",
        "Bonus postponed",
        "Delay in performance bonus",
        "Bonus not processed yet",
        "Bonus not reflecting yet",
        
        # Salary breakup queries
        "Salary breakup not matching",
        "Confused about payslip",
        "Basic and allowance mismatch",
        "Deductions not explained",
        "Wrong CTC split",
        "Breakup in payslip is wrong",
        "Not able to understand salary structure",
        "This salary structure is different",
        "My pay components changed",
        
        # HRA queries
        "HRA not showing",
        "Rent not counted",
        "HRA rejected",
        "Rent receipts uploaded but not accepted",
        "HRA claim not processed",
        "HRA missing from salary",
        "No HRA deduction",
        "No exemption on rent",
        "No HRA benefit",
        "HRA not visible in payslip",
        
        # Escalation queries
        "Please escalate salary issue",
        "Need to raise complaint about salary",
        "Requesting escalation for payroll",
        "Escalate to HR",
        "Raise this issue",
        "Kindly escalate",
        "Forward to concerned team",
        "Take this to manager",
        "Need urgent resolution",
        "This is serious, escalate please",
        
        # Manual escalation queries
        "I want to talk to HR",
        "Raise a ticket",
        "Submit a complaint",
        "Need help from senior",
        "Take this up",
        "File escalation",
        "Contact escalation team",
        "Forward this issue",
        "Register complaint",
        "Submit issue to payroll escalation",
        
        # Reimbursement queries
        "Reimbursement not paid",
        "Bills uploaded but no payment",
        "Travel claim not received",
        "Expense claim rejected",
        "Mobile bill not processed",
        "Pending reimbursements",
        "Submitted receipts ignored",
        "Reimbursement skipped",
        "My expenses not reimbursed",
        
        # Leave encashment queries
        "EL encashment not credited",
        "Leave not encashed",
        "Leave payout missing",
        "Pending leave encashment",
        "Leave balance not paid",
        "Leave not settled",
        "Earned leaves not monetized",
        "EL not shown in salary",
        "Leave encashment skipped",
        
        # Investment proof queries
        "Proofs rejected",
        "Investment proof not accepted",
        "Submitted documents not considered",
        "I gave LIC but not accepted",
        "HRA proof denied",
        "Proof rejected without reason",
        "Upload was successful but ignored",
        "My proofs didn't go through",
        "Rejection of proofs",
        "Investments rejected",
        
        # Text extraction queries
        "Extract all dates from this document.",
        "Summarize the following file.",
        "Identify all named entities in this file.",
        "Route this query to the appropriate intent handler."
    ]

    for query in test_queries:
        result = classifier.classify_intent(query)
        print(f"Query: {query}")
        print(f"Result: {result}")
        print(f"Calibration Info for query: Temp={result.get('calibration_info', {}).get('temperature', 'N/A'):.4f}, AutoCalibrated={result.get('calibration_info', {}).get('auto_calibrated', 'N/A')}\n")

    initial_calibration_info = classifier.get_calibration_info()
    print("\n=== Calibration Information ===")
    print(f"Auto-calibrated Temperature: {initial_calibration_info['temperature']:.4f}")
    print(f"Calibration Method: {initial_calibration_info['calibration_metrics'].get('method', 'N/A')}")

    ece_value = initial_calibration_info['calibration_metrics'].get('expected_calibration_error', None)
    ece_str = f"{ece_value:.4f}" if ece_value is not None else "N/A"
    print(f"Expected Calibration Error: {ece_str}")

    nll_value = initial_calibration_info['calibration_metrics'].get('negative_log_likelihood', None)
    nll_str = f"{nll_value:.4f}" if nll_value is not None else "N/A"
    print(f"Negative Log Likelihood: {nll_str}")

    accuracy_value = initial_calibration_info['calibration_metrics'].get('accuracy', None)
    accuracy_str = f"{accuracy_value:.4f}" if accuracy_value is not None else "N/A"
    print(f"Model Accuracy: {accuracy_str}")


    print("\n=== Adding new training samples and triggering retrain with auto-recalibration ===")
    new_samples = [
        {"query": "I need info about PTO", "intent": "leave"},
        {"query": "Tell me about health insurance", "intent": "benefits"},
        {"query": "How much vacation do I get?", "intent": "leave"},
        {"query": "What's the retirement plan?", "intent": "benefits"},
        {"query": "I want to know about stock options", "intent": "compensation"}
    ]
    for sample in new_samples:
        classifier.update_training_data(sample["query"], sample["intent"])
        print(f"Added new sample: '{sample['query']}' -> '{sample['intent']}'")

    print("Waiting 10 seconds for retraining and auto-recalibration to complete...")
    time.sleep(10)

    print("\n=== Classification Results After Update and Auto-Recalibration ===")
    for query in test_queries:
        result = classifier.classify_intent(query)
        intent = result.get("intent", "unknown")
        confidence = result.get("confidence", 0.0)
        # Safe float formatting for temperature
        temp = result.get('calibration_info', {}).get('temperature', 'N/A')
        if isinstance(temp, (float, int)):
            temp_str = f"{temp:.4f}"
        else:
            temp_str = str(temp)
        auto_cal = result.get('calibration_info', {}).get('auto_calibrated', 'N/A')
        print(f"Query: {query}\n  → Predicted intent: {intent} (confidence: {confidence:.2f})\n  Calibration Temp: {temp_str}, AutoCalibrated: {auto_cal}\n")

    updated_calibration_info = classifier.get_calibration_info()
    print("\n=== Updated Calibration Information ===")
    print(f"Updated Auto-calibrated Temperature: {updated_calibration_info['temperature']:.4f}")
    print(f"Calibration Method: {updated_calibration_info['calibration_metrics'].get('method', 'N/A')}")

    ece_value = updated_calibration_info['calibration_metrics'].get('expected_calibration_error', None)
    ece_str = f"{ece_value:.4f}" if ece_value is not None else "N/A"
    print(f"Expected Calibration Error: {ece_str}")

    nll_value = updated_calibration_info['calibration_metrics'].get('negative_log_likelihood', None)
    nll_str = f"{nll_value:.4f}" if nll_value is not None else "N/A"
    print(f"Negative Log Likelihood: {nll_str}")

    accuracy_value = updated_calibration_info['calibration_metrics'].get('accuracy', None)
    accuracy_str = f"{accuracy_value:.4f}" if accuracy_value is not None else "N/A"
    print(f"Model Accuracy: {accuracy_str}")

    if updated_calibration_info['calibration_history']:
        print(f"Calibration History: {len(updated_calibration_info['calibration_history'])} calibrations performed")
        for i, cal in enumerate(updated_calibration_info['calibration_history']):
            ece_cal = cal.get('expected_calibration_error', None)
            ece_cal_str = f"{ece_cal:.4f}" if ece_cal is not None else "N/A"
            
            nll_cal = cal.get('negative_log_likelihood', None)
            nll_cal_str = f"{nll_cal:.4f}" if nll_cal is not None else "N/A"
            
            acc_cal = cal.get('accuracy', None)
            acc_cal_str = f"{acc_cal:.4f}" if acc_cal is not None else "N/A"
            
            print(f"Calibration {i+1}: Temp={cal.get('temperature', 'N/A'):.4f}, Method={cal.get('method', 'N/A')}, ECE={ece_cal_str}, NLL={nll_cal_str}, Accuracy={acc_cal_str}")
            
    print("\n=== Training and Testing Complete ===")
    print("Intent classifier is now ready for use!")