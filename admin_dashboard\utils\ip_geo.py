import aiohttp

async def get_geo_from_ip(ip: str) -> dict:
    """
    Returns geolocation info for an IP address using ipapi.co.
    Output: {"country": str or None, "region": str or None, "city": str or None}
    """
    url = f"https://ipapi.co/{ip}/json/"
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=5) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    return {
                        "country": data.get("country_name"),
                        "region": data.get("region"),
                        "city": data.get("city"),
                    }
    except Exception:
        pass
    return {"country": None, "region": None, "city": None} 