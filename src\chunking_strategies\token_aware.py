import re
from typing import List
from .base import Chunking<PERSON>trate<PERSON>, Chunking<PERSON>onfig, TokenizerManager
import numpy as np
import random

class TokenAwareStrategy(ChunkingStrategy):
    """Token-aware text chunking strategy with adaptive sizing and overlap control."""

    def get_name(self) -> str:
        return "token"

    def _split_into_sentences(self, text: str) -> list:
        """Split text into sentences using regex patterns."""
        import re
        # Basic fallback to split on sentence-ending punctuation or newlines
        sentences = re.split(r'(?<=[.!?])\s+|\n+', text)
        return [s.strip() for s in sentences if s.strip()]

    def _join_with_space(self, chunk1: str, chunk2: str) -> str:
        """Join two text chunks with appropriate spacing."""
        if not chunk1:
            return chunk2
        if not chunk2:
            return chunk1

        # Add space if the first chunk doesn't end with whitespace
        # and the second chunk doesn't start with whitespace
        if chunk1 and chunk2:
            if not chunk1.endswith((' ', '\n', '\t')) and not chunk2.startswith((' ', '\n', '\t')):
                return f"{chunk1} {chunk2}"
            else:
                return f"{chunk1}{chunk2}"
        return chunk1 + chunk2

    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        if not config.use_tokens:
            return self._fallback_char_chunking(text, config)

        # Adaptive chunk sizing
        max_tokens = config.max_tokens
        if getattr(config, 'adaptive', False):
            sentences = self._split_into_sentences(text)
            avg_len = np.mean([tokenizer_manager.count_tokens(s, config.tokenizer_name) for s in sentences])
            # Example: scale max_tokens up for longer sentences, down for shorter
            if avg_len > 40:
                max_tokens = min(1024, int(avg_len * 1.5))
            elif avg_len < 15:
                max_tokens = max(256, int(avg_len * 2))
            # else leave as is
        else:
            sentences = self._split_into_sentences(text)

        chunks = []
        current_chunk = ""
        for sentence in sentences:
            test_chunk = self._join_with_space(current_chunk, sentence)
            token_count = tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name)
            if token_count <= max_tokens:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                if tokenizer_manager.count_tokens(sentence, config.tokenizer_name) > max_tokens:
                    chunks.extend(self._split_oversized_sentence(sentence, config, tokenizer_manager))
                else:
                    current_chunk = sentence
        if current_chunk:
            chunks.append(current_chunk.strip())

        # Temperature-controlled overlap
        if getattr(config, 'temperature_overlap', False):
            # Example: vary overlap based on sentence length or randomly
            overlapped_chunks = [chunks[0]] if chunks else []
            for i in range(1, len(chunks)):
                prev_chunk = chunks[i-1]
                current_chunk = chunks[i]
                # Example: overlap more if previous chunk is long, less if short
                prev_len = tokenizer_manager.count_tokens(prev_chunk, config.tokenizer_name)
                if prev_len > max_tokens * 0.8:
                    overlap_tokens = int(max_tokens * 0.2)
                elif prev_len < max_tokens * 0.5:
                    overlap_tokens = int(max_tokens * 0.05)
                else:
                    overlap_tokens = int(max_tokens * 0.1)
                # Or: overlap_tokens = random.randint(0, int(max_tokens * 0.2))
                overlap = self._get_token_overlap(prev_chunk, overlap_tokens, config, tokenizer_manager)
                if overlap and not current_chunk.startswith(overlap):
                    overlapped_chunks.append(f"{overlap} {current_chunk}")
                else:
                    overlapped_chunks.append(current_chunk)
            return overlapped_chunks
        else:
            return self._add_token_overlap(chunks, config, tokenizer_manager)

    def _fallback_char_chunking(self, text: str, config: ChunkingConfig) -> List[str]:
        """Fallback character-based chunking when tokens are not used."""
        if len(text) <= config.chunk_size:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            end = start + config.chunk_size

            # If we're not at the end of the text, try to break at word boundary
            if end < len(text):
                # Look for the last space within the chunk
                last_space = text.rfind(' ', start, end)
                if last_space > start:
                    end = last_space

            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)

            # Move start position, accounting for overlap
            start = end - config.chunk_overlap if config.chunk_overlap > 0 else end

            # Ensure we make progress
            if start <= end - config.chunk_size:
                start = end

        return chunks

    def _split_oversized_sentence(self, sentence: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        """Split a sentence that exceeds the token limit into smaller chunks."""
        max_tokens = config.max_tokens

        # First try to split by punctuation
        parts = re.split(r'([,;:])', sentence)
        if len(parts) > 1:
            chunks = []
            current_chunk = ""

            for part in parts:
                test_chunk = self._join_with_space(current_chunk, part)
                if tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name) <= max_tokens:
                    current_chunk = test_chunk
                else:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    current_chunk = part

            if current_chunk:
                chunks.append(current_chunk.strip())

            # If we still have oversized chunks, split by words
            final_chunks = []
            for chunk in chunks:
                if tokenizer_manager.count_tokens(chunk, config.tokenizer_name) > max_tokens:
                    final_chunks.extend(self._split_by_words(chunk, config, tokenizer_manager))
                else:
                    final_chunks.append(chunk)

            return final_chunks

        # If no punctuation, split by words
        return self._split_by_words(sentence, config, tokenizer_manager)

    def _split_by_words(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        """Split text by words when it exceeds token limits."""
        words = text.split()
        chunks = []
        current_chunk = ""

        for word in words:
            test_chunk = self._join_with_space(current_chunk, word)
            if tokenizer_manager.count_tokens(test_chunk, config.tokenizer_name) <= config.max_tokens:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = word

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    def _get_token_overlap(self, text: str, overlap_tokens: int, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> str:
        """Get the last N tokens from text for overlap purposes."""
        if not text or overlap_tokens <= 0:
            return ""

        words = text.split()
        if not words:
            return ""

        # Start from the end and work backwards
        overlap_text = ""
        for i in range(len(words) - 1, -1, -1):
            test_overlap = " ".join(words[i:])
            token_count = tokenizer_manager.count_tokens(test_overlap, config.tokenizer_name)

            if token_count <= overlap_tokens:
                overlap_text = test_overlap
            else:
                break

        return overlap_text

    def _add_token_overlap(self, chunks: List[str], config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        """Add token-based overlap between chunks."""
        if not chunks or len(chunks) <= 1 or config.token_overlap <= 0:
            return chunks

        overlapped_chunks = [chunks[0]]

        for i in range(1, len(chunks)):
            prev_chunk = chunks[i-1]
            current_chunk = chunks[i]

            # Get overlap from previous chunk
            overlap = self._get_token_overlap(prev_chunk, config.token_overlap, config, tokenizer_manager)

            if overlap and not current_chunk.startswith(overlap):
                overlapped_chunk = self._join_with_space(overlap, current_chunk)
                overlapped_chunks.append(overlapped_chunk)
            else:
                overlapped_chunks.append(current_chunk)

        return overlapped_chunks