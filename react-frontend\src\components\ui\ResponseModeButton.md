# ResponseModeButton Component

A reusable React component that provides a dropdown menu for selecting between "Detailed" and "Concise" response modes, similar to <PERSON>'s interface.

## Features

- **Multiple Variants**: Default, outline, ghost, and compact styles
- **Different Sizes**: Small, default, and large sizes
- **Visual Feedback**: Shows selected mode with green indicator
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Dark Mode Support**: Fully compatible with dark/light themes
- **TypeScript**: Fully typed with TypeScript interfaces

## Usage

### Basic Usage

```tsx
import ResponseModeButton from '@/components/ui/ResponseModeButton';

const MyComponent = () => {
  const [responseMode, setResponseMode] = useState<'detailed' | 'concise'>('detailed');

  return (
    <ResponseModeButton
      responseMode={responseMode}
      onResponseModeChange={setResponseMode}
    />
  );
};
```

### Variants

#### Default Variant
```tsx
<ResponseModeButton
  responseMode={responseMode}
  onResponseModeChange={setResponseMode}
  variant="default"
/>
```

#### Outline Variant
```tsx
<ResponseModeButton
  responseMode={responseMode}
  onResponseModeChange={setResponseMode}
  variant="outline"
/>
```

#### Ghost Variant
```tsx
<ResponseModeButton
  responseMode={responseMode}
  onResponseModeChange={setResponseMode}
  variant="ghost"
/>
```

#### Compact Variant (Icon Only)
```tsx
<ResponseModeButton
  responseMode={responseMode}
  onResponseModeChange={setResponseMode}
  variant="compact"
/>
```

### Sizes

```tsx
// Small
<ResponseModeButton size="sm" />

// Default
<ResponseModeButton size="default" />

// Large
<ResponseModeButton size="lg" />
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `responseMode` | `'detailed' \| 'concise'` | - | Current response mode (required) |
| `onResponseModeChange` | `(mode: 'detailed' \| 'concise') => void` | - | Callback when mode changes (required) |
| `variant` | `'default' \| 'outline' \| 'ghost' \| 'compact'` | `'outline'` | Visual style variant |
| `size` | `'sm' \| 'default' \| 'lg'` | `'default'` | Size of the button |
| `className` | `string` | - | Additional CSS classes |

## Integration Examples

### In Header
```tsx
import HeaderWithResponseMode from '@/components/layout/HeaderWithResponseMode';

const App = () => {
  const [responseMode, setResponseMode] = useState<'detailed' | 'concise'>('detailed');

  return (
    <HeaderWithResponseMode
      user={user}
      onOpenSettings={handleOpenSettings}
      onLogout={handleLogout}
      responseMode={responseMode}
      onResponseModeChange={setResponseMode}
    />
  );
};
```

### In Chat Input
The component is already integrated into both `ChatInputBox` and `ChatInput` components with a settings icon.

### Standalone Usage
```tsx
import ResponseModeButton from '@/components/ui/ResponseModeButton';

const SettingsPanel = () => {
  const [responseMode, setResponseMode] = useState<'detailed' | 'concise'>('detailed');

  return (
    <div className="p-4">
      <h3>Response Settings</h3>
      <ResponseModeButton
        responseMode={responseMode}
        onResponseModeChange={setResponseMode}
        variant="outline"
        size="lg"
      />
    </div>
  );
};
```

## Styling

The component uses Tailwind CSS classes and follows the design system. It automatically adapts to dark/light themes and provides hover/focus states.

### Custom Styling
```tsx
<ResponseModeButton
  responseMode={responseMode}
  onResponseModeChange={setResponseMode}
  className="bg-blue-500 text-white hover:bg-blue-600"
/>
```

## Backend Integration

The response mode is automatically passed to the API when sending messages:

```tsx
// In api.ts
export const chatAPI = {
  sendMessage: async (message: string, files?: File[], deviceId?: string, email?: string, employeeId?: string, chatId?: string, responseMode?: string) => {
    const payload: any = {
      query: message,
      device_id: deviceId || 'web-client',
      files_info: files ? files.map((file) => ({ name: file.name, size: file.size, type: file.type })) : [],
      response_mode: responseMode || 'detailed', // Uses the selected mode
    };
    // ... rest of the implementation
  },
};
```

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Focus management
- Screen reader friendly
- High contrast support

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- IE11+ (with polyfills)
- Mobile browsers 