import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getAuditLogs } from "@/services/api";
import { AuditLog } from '../../types';
import { Layout } from '../../components/Layout';
import { ProtectedRoute } from '../../components/ProtectedRoute';

function filterLogs(logs: AuditLog[], email: string, action: string, date: string) {
  return logs.filter(log =>
    (!email || log.admin_email?.includes(email)) &&
    (!action || log.action.includes(action)) &&
    (!date || log.timestamp.startsWith(date))
  );
}

export default function AuditPage() {
  const [email, setEmail] = useState('');
  const [action, setAction] = useState('');
  const [date, setDate] = useState('');
  const [expanded, setExpanded] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const pageSize = 20;

  const { data, isLoading, error } = useQuery({
    queryKey: ['auditLogs'],
    queryFn: async () => {
      try {
        const res = await getAuditLogs();
        return res.data.logs as AuditLog[];
      } catch {
        return [];
      }
    },
  });

  const filtered = data ? filterLogs(data, email, action, date) : [];
  const paginated = filtered.slice((page - 1) * pageSize, page * pageSize);
  const totalPages = Math.ceil(filtered.length / pageSize);

  return (
    <ProtectedRoute roles={['SUPERADMIN']}>
      <h1 className="text-2xl font-bold mb-6">Audit Logs</h1>
      <div className="flex gap-4 mb-4">
        <input
          type="text"
          placeholder="Filter by email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="border rounded px-2 py-1"
        />
        <input
          type="text"
          placeholder="Filter by action"
          value={action}
          onChange={e => setAction(e.target.value)}
          className="border rounded px-2 py-1"
        />
        <input
          type="date"
          value={date}
          onChange={e => setDate(e.target.value)}
          className="border rounded px-2 py-1"
        />
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full bg-neutral-900 text-neutral-100 rounded shadow border border-neutral-800">
          <thead>
            <tr>
              <th className="px-4 py-2 text-left">Timestamp</th>
              <th className="px-4 py-2">Email</th>
              <th className="px-4 py-2">Action</th>
              <th className="px-4 py-2">IP</th>
              <th className="px-4 py-2">Metadata</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr><td colSpan={5} className="text-center py-8">Loading...</td></tr>
            ) : error ? (
              <tr><td colSpan={5} className="text-center text-yellow-500">Failed to load logs from server. Showing mock data.</td></tr>
            ) : paginated.length === 0 ? (
              <tr><td colSpan={5} className="text-center py-8">No logs found</td></tr>
            ) : paginated.map(log => (
              <>
                <tr key={log.id} className="border-b">
                  <td className="px-4 py-2 font-mono text-xs">{log.timestamp}</td>
                  <td className="px-4 py-2">{log.admin_email}</td>
                  <td className="px-4 py-2">{log.action}</td>
                  <td className="px-4 py-2">{log.ip_address}</td>
                  <td className="px-4 py-2">
                    <button
                      className="text-blue-600 underline text-xs"
                      onClick={() => setExpanded(expanded === log.id ? null : log.id)}
                    >
                      {expanded === log.id ? 'Hide' : 'View'}
                    </button>
                  </td>
                </tr>
                {expanded === log.id && (
                  <tr>
                    <td colSpan={5} className="bg-gray-50 px-4 py-2">
                      <pre className="text-xs overflow-x-auto">{JSON.stringify(log.metadata, null, 2)}</pre>
                    </td>
                  </tr>
                )}
              </>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-4">
        <div>Page {page} of {totalPages || 1}</div>
        <div className="space-x-2">
          <button
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
            className="px-2 py-1 border rounded disabled:opacity-50"
          >Prev</button>
          <button
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
            className="px-2 py-1 border rounded disabled:opacity-50"
          >Next</button>
        </div>
      </div>
    </ProtectedRoute>
  );
} 