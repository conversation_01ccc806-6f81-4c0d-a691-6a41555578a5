import React from 'react';
import { motion } from 'framer-motion';
import { Plus, Upload } from 'lucide-react';

interface FloatingActionButtonProps {
  onNewChat: () => void;
  onUpload?: () => void;
  className?: string;
}

/**
 * FloatingActionButton - A premium, animated FAB for quick actions (new chat, upload).
 * - Blue-accented, round, shadowed, with hover/focus/active states.
 * - Accessible (aria-labels, keyboard focus).
 * - Modular: accepts onNewChat and optional onUpload handlers.
 */
const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  onNewChat,
  onUpload,
  className = '',
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ type: 'spring', stiffness: 400, damping: 30 }}
      className={`fixed bottom-8 right-8 z-50 flex flex-col gap-3 items-end ${className}`}
    >
      {onUpload && (
        <button
          aria-label="Upload file"
          onClick={onUpload}
          className="w-14 h-14 rounded-full bg-blue-500 text-white shadow-lg flex items-center justify-center mb-2 transition hover:bg-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-300"
        >
          <Upload className="w-6 h-6" />
        </button>
      )}
      <button
        aria-label="New chat"
        onClick={onNewChat}
        className="w-16 h-16 rounded-full bg-blue-600 text-white shadow-xl flex items-center justify-center text-3xl transition hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300"
      >
        <Plus className="w-8 h-8" />
      </button>
    </motion.div>
  );
};

export default FloatingActionButton; 