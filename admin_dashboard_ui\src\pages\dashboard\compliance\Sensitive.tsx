import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select } from "@/components/ui/select"; // Assume stub
import { Skeleton } from "@/components/ui/skeleton";
import { useReactTable, getCoreRowModel, flexRender, ColumnDef, getSortedRowModel, SortingState } from "@tanstack/react-table";
import api from "@/services/api";

const PAGE_SIZE = 10;
const TYPES = ["All", "PII", "Financial", "Other"];
const REVIEWED = ["All", "Yes", "No"];

// Define the type for a sensitive flag row
interface SensitiveFlagRow {
  id: string;
  chat_id: string;
  type: string;
  detected_on: string;
  reviewed: string;
}

const columns: ColumnDef<SensitiveFlagRow>[] = [
  { accessorKey: "chat_id", header: "Chat ID" },
  { accessorKey: "type", header: "Type" },
  { accessorKey: "detected_on", header: "Detected On" },
  { accessorKey: "reviewed", header: "Reviewed" },
  { accessorKey: "action", header: "Action" },
];

const SensitiveFlags = () => {
  const [dateRange, setDateRange] = useState("7d");
  const [type, setType] = useState("All");
  const [reviewed, setReviewed] = useState("All");
  const [page, setPage] = useState(1);
  const [data, setData] = useState<SensitiveFlagRow[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = [
      `range=${dateRange}`,
      type !== "All" ? `type=${encodeURIComponent(type)}` : null,
      reviewed !== "All" ? `reviewed=${encodeURIComponent(reviewed)}` : null,
      `page=${page}`,
      `pageSize=${PAGE_SIZE}`,
    ]
      .filter(Boolean)
      .join("&");
    api
      .get(`/compliance/sensitive-flags?${params}`)
      .then((res) => {
        setData(res.data.flags || []);
        setTotal(res.data.total || 0);
      })
      .catch((err) => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [dateRange, type, reviewed, page]);

  const handleMarkReviewed = (id: string) => {
    alert(`Marked flag ${id} as reviewed (mock)`);
  };

  const table = useReactTable({
    data,
    columns: columns.map(col =>
      col.id === "action"
        ? {
            ...col,
            cell: ({ row }) =>
              row.original.reviewed === "No" ? (
                <Button size="sm" variant="outline" onClick={() => handleMarkReviewed(row.original.id)}>
                  Mark Reviewed
                </Button>
              ) : null,
          }
        : col
    ) as ColumnDef<SensitiveFlagRow, any>[],
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: { sorting },
    onSortingChange: setSorting,
    manualSorting: false,
  });

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  const totalPages = Math.ceil(total / PAGE_SIZE);

  return (
    <div className="max-w-5xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle>Sensitive Data Flags</CardTitle>
          <div className="flex flex-wrap gap-2 items-center">
            <Select value={dateRange} onValueChange={setDateRange}>
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="custom">Custom</option>
            </Select>
            <Select value={type} onValueChange={setType}>
              {TYPES.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
            <Select value={reviewed} onValueChange={setReviewed}>
              {REVIEWED.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
              <thead className="bg-muted dark:bg-zinc-800">
                {table.getHeaderGroups().map(headerGroup => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <th
                        key={header.id}
                        className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase cursor-pointer"
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getIsSorted() ? (header.column.getIsSorted() === "asc" ? " ▲" : " ▼") : ""}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody className="bg-background dark:bg-zinc-900">
                {table.getRowModel().rows.map(row => (
                  <tr key={row.id} className="border-b border-border dark:border-zinc-800">
                    {row.getVisibleCells().map(cell => (
                      <td key={cell.id} className="px-4 py-2 text-sm">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {/* Pagination controls */}
          <div className="flex justify-end gap-2 items-center mt-4">
            <Button size="sm" variant="ghost" disabled={page === 1} onClick={() => setPage(page - 1)}>
              Previous
            </Button>
            <span className="text-xs text-muted-foreground">
              Page {page} of {totalPages || 1}
            </span>
            <Button size="sm" variant="ghost" disabled={page === totalPages || totalPages === 0} onClick={() => setPage(page + 1)}>
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SensitiveFlags; 