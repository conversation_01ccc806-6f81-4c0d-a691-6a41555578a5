"""
API Rate Limiter for Flask endpoints (production-ready).

Supports:
- Per-IP and per-user rate limiting
- Configurable limits (requests per minute/hour/day)
- In-memory and Redis backends
- Burst and sustained rate limiting
- Custom error responses
- Decorator for easy integration
"""
import time
import threading
from functools import wraps
from flask import request, jsonify, g
from collections import defaultdict, deque
import os

try:
    import redis
except ImportError:
    redis = None


class InMemoryRateLimiter:
    """In-memory rate limiter for development and small-scale production."""
    def __init__(self, limit: int, window: int):
        self.limit = limit
        self.window = window  # seconds
        self.data = defaultdict(deque)
        self.lock = threading.RLock()

    def is_allowed(self, key: str) -> bool:
        now = time.time()
        with self.lock:
            dq = self.data[key]
            # Remove old timestamps
            while dq and dq[0] < now - self.window:
                dq.popleft()
            if len(dq) < self.limit:
                dq.append(now)
                return True
            return False

    def get_remaining(self, key: str) -> int:
        now = time.time()
        with self.lock:
            dq = self.data[key]
            while dq and dq[0] < now - self.window:
                dq.popleft()
            return max(0, self.limit - len(dq))


class RedisRateLimiter:
    """Redis-backed rate limiter for distributed production deployments."""
    def __init__(self, limit: int, window: int, redis_url: str):
        if not redis:
            raise ImportError("redis-py is required for RedisRateLimiter")
        self.limit = limit
        self.window = window
        self.redis = redis.StrictRedis.from_url(redis_url)

    def is_allowed(self, key: str) -> bool:
        now = int(time.time())
        pipe = self.redis.pipeline()
        pipe.zremrangebyscore(key, 0, now - self.window)
        pipe.zadd(key, {now: now})
        pipe.zcard(key)
        pipe.expire(key, self.window)
        _, _, count, _ = pipe.execute()
        return count <= self.limit

    def get_remaining(self, key: str) -> int:
        now = int(time.time())
        self.redis.zremrangebyscore(key, 0, now - self.window)
        count = self.redis.zcard(key)
        return max(0, self.limit - count)


def get_rate_limiter(limit: int, window: int):
    """Get the appropriate rate limiter based on environment."""
    redis_url = os.environ.get("REDIS_URL")
    if redis_url and redis:
        return RedisRateLimiter(limit, window, redis_url)
    return InMemoryRateLimiter(limit, window)


def rate_limit(limit: int = 60, window: int = 60, by_ip: bool = True, by_user: bool = False, error_message: str = None):
    """Decorator to rate limit Flask endpoints."""
    limiter = get_rate_limiter(limit, window)
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if by_user and hasattr(g, 'user') and getattr(g.user, 'id', None):
                key = f"user:{g.user.id}"
            elif by_ip:
                key = f"ip:{request.remote_addr}"
            else:
                key = "global"
            if not limiter.is_allowed(key):
                remaining = limiter.get_remaining(key)
                resp = {
                    "success": False,
                    "error": error_message or f"Rate limit exceeded. Try again later.",
                    "retry_after_seconds": window,
                    "remaining": remaining
                }
                return jsonify(resp), 429
            return func(*args, **kwargs)
        return wrapper
    return decorator 