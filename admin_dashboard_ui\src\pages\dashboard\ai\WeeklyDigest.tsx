import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import api from "@/services/api";
import { motion } from "framer-motion";

const WeeklyDigest = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    api
      .get("/ai/weekly-digest")
      .then((res) => setData(res.data))
      .catch((err) => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, []);

  const handleExportPDF = () => {
    alert("Export to PDF (mock)");
  };
  const handleScheduleEmail = () => {
    alert("Schedule Email (mock)");
  };

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data) return <div className="text-muted-foreground p-4">No data available.</div>;

  const cards = [
    {
      title: "Top Queries",
      content: data.top_queries?.join(", ") || "-",
    },
    {
      title: "Sentiment Shifts",
      content: data.sentiment_shifts || "-",
    },
    {
      title: "Escalation Summary",
      content: data.escalation_summary || "-",
    },
    {
      title: "Training Suggestions",
      content: data.training_suggestions?.join(", ") || "-",
    },
  ];

  return (
    <div className="max-w-5xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle>Weekly AI Digest</CardTitle>
          <div className="flex flex-wrap gap-2 items-center">
            <Button size="sm" variant="outline" onClick={handleExportPDF}>Export PDF</Button>
            <Button size="sm" variant="outline" onClick={handleScheduleEmail}>Schedule Email</Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {cards.map((card, i) => (
              <div
                key={card.title}
                className="p-4 rounded shadow bg-muted dark:bg-zinc-800"
              >
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.1 }}
                  style={{ transform: 'translateZ(0)' }}
                >
                  <div className="font-semibold mb-2">{card.title}</div>
                  <div className="text-sm text-muted-foreground">{card.content}</div>
                </motion.div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WeeklyDigest; 