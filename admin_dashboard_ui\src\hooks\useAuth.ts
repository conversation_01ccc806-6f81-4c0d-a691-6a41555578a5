import { useState, useEffect, useCallback } from 'react';
import { jwtDecode } from 'jwt-decode';
import { Role, Roles } from '@/types';
import { useAuthStore } from './useAuthStore';

interface AuthState {
  user: string | null;
  role: Role | null;
  token: string | null;
}

export function useAuth() {
  // Replace mock values with real store values
  const { user, role, token, login, logout } = useAuthStore();
  return {
    user,
    role,
    token,
    login,
    logout,
  };
} 