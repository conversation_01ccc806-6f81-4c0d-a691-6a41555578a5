import React, { useEffect, useState } from 'react';
import { ChevronLeft, MessageSquare, Clock, CheckCircle } from 'lucide-react';
import { escalationAPI } from '@/utils/api';

interface EscalationListProps {
  onBack: () => void;
}

interface FollowUp {
  message: string;
  date: string;
  type: 'employee' | 'hr';
}

const EscalationList: React.FC<EscalationListProps> = ({ onBack }) => {
  const [escalations, setEscalations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedIdx, setSelectedIdx] = useState<number | null>(null);
  const [followUpMsg, setFollowUpMsg] = useState('');
  const [sendingReply, setSendingReply] = useState(false);

  useEffect(() => {
    const fetchEscalations = async (retryCount = 0) => {
      setLoading(true);
      setError(null);
      try {
        const accessToken = localStorage.getItem('access_token') || localStorage.getItem('token'); // Fallback for legacy
        console.log('DEBUG: access_token from localStorage:', accessToken);
        
        const res = await fetch('http://localhost:5052/api/escalations', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {})
          }
        });
        if (!res.ok) throw new Error('Failed to fetch escalations');
        const data = await res.json();
        const escs = Array.isArray(data) ? data : (Array.isArray(data.escalations) ? data.escalations : []);
        setEscalations(escs);
        if (escs.length > 0) setSelectedIdx(0);
      } catch (err: any) {
        if (err.message.includes('Failed to fetch') || err.message.includes('NetworkError') || err.message.includes('ERR_CONNECTION_REFUSED')) {
          if (retryCount < 2) {
            // Retry after 2 seconds
            setTimeout(() => fetchEscalations(retryCount + 1), 2000);
            return;
          }
          setError('Escalation server is not available. Please ensure the escalation server is running on port 5052.');
        } else {
          setError(err.message || 'Unknown error');
        }
      } finally {
        setLoading(false);
      }
    };
    fetchEscalations();
  }, []);

  const handleSelect = (idx: number) => setSelectedIdx(idx);

  const handleFollowUpSend = async () => {
    if (selectedIdx === null || !followUpMsg.trim() || sendingReply) return;
    
    setSendingReply(true);
    try {
      const escalation = escalations[selectedIdx];
      const accessToken = localStorage.getItem('access_token') || localStorage.getItem('token');
      
      const response = await fetch(`http://localhost:5052/api/escalations/${escalation.id}/reply`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {})
          },
          body: JSON.stringify({
            reply: followUpMsg,
            replied_by: localStorage.getItem('user_email') || 'employee',
            reply_type: 'employee'
          })
        });
      
      if (response.ok) {
        setFollowUpMsg('');
        // Refresh escalations to get updated data
        const res = await fetch('http://localhost:5052/api/escalations', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            ...(localStorage.getItem('access_token') ? { Authorization: `Bearer ${localStorage.getItem('access_token')}` } : {})
          }
        });
        if (res.ok) {
          const data = await res.json();
          const escs = Array.isArray(data) ? data : (Array.isArray(data.escalations) ? data.escalations : []);
          setEscalations(escs);
        }
      } else {
        setError('Failed to send reply');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to send reply');
    } finally {
      setSendingReply(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'assigned':
        return <MessageSquare className="w-4 h-4 text-blue-500" />;
      case 'replied':
      case 'resolved':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'assigned':
        return 'text-blue-600 bg-blue-100';
      case 'replied':
      case 'resolved':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="flex h-[80vh] w-full bg-white rounded shadow overflow-hidden">
      {/* Sidebar */}
      <div className="w-[350px] min-w-[250px] max-w-[400px] border-r bg-gray-50 overflow-y-auto flex flex-col">
        <button onClick={onBack} className="m-3 p-2 rounded-full hover:bg-gray-200 w-8 h-8 flex items-center justify-center" title="Back">
          <ChevronLeft className="w-5 h-5" />
        </button>
        <h2 className="text-lg font-bold px-4 mb-2">My Escalations</h2>
        {loading ? (
          <div className="px-4">Loading...</div>
        ) : error ? (
          <div className="text-red-500 px-4">
            {error}
            {error.includes('Escalation server is not available') && (
              <button 
                onClick={() => {
                  setError(null);
                  const fetchEscalations = async () => {
                    setLoading(true);
                    try {
                      const accessToken = localStorage.getItem('access_token') || localStorage.getItem('token'); // Fallback for legacy
                      console.log('DEBUG: Retry - access_token from localStorage:', accessToken);
                      
                      const res = await fetch('http://localhost:5052/api/escalations', {
                        credentials: 'include',
                        headers: {
                          'Content-Type': 'application/json',
                          ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {})
                        }
                      });
                      if (!res.ok) throw new Error('Failed to fetch escalations');
                      const data = await res.json();
                      const escs = Array.isArray(data) ? data : (Array.isArray(data.escalations) ? data.escalations : []);
                      setEscalations(escs);
                      if (escs.length > 0) setSelectedIdx(0);
                    } catch (err: any) {
                      setError('Escalation server is still not available. Please ensure the escalation server is running on port 5052.');
                    } finally {
                      setLoading(false);
                    }
                  };
                  fetchEscalations();
                }}
                className="ml-2 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
              >
                Retry
              </button>
            )}
          </div>
        ) : escalations.length === 0 ? (
          <div className="px-4">No escalations found.</div>
        ) : (
          <ul className="flex-1">
            {escalations.map((esc, idx) => (
              <li
                key={esc.id || idx}
                className={`px-4 py-3 border-b cursor-pointer hover:bg-blue-50 ${selectedIdx === idx ? 'bg-blue-100 font-semibold border-l-4 border-blue-500' : ''}`}
                onClick={() => handleSelect(idx)}
              >
                <div className="flex items-center justify-between">
                  <span className="font-bold">{esc.issue_type || 'N/A'}</span>
                  {getStatusIcon(esc.status || 'pending')}
                </div>
                <div className="truncate text-gray-600 text-sm mt-1">{esc.issue_description?.slice(0, 40) || ''}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {esc.created_at ? new Date(esc.created_at).toLocaleDateString() : ''}
                  {esc.assigned_to && (
                    <span className="ml-2">• Assigned to: {esc.assigned_to}</span>
                  )}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
      {/* Main Thread Panel */}
      <div className="flex-1 flex flex-col bg-gray-50">
        {selectedIdx !== null && escalations[selectedIdx] ? (
          <div className="p-6 flex-1 flex flex-col">
            {/* Escalation details header */}
            <div className="mb-4 p-4 rounded border bg-white shadow-sm">
              <div className="flex items-center justify-between mb-2">
                <span className="font-bold text-lg">{escalations[selectedIdx].issue_type || 'N/A'}</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(escalations[selectedIdx].status || 'pending')}`}>
                  {escalations[selectedIdx].status || 'pending'}
                </span>
              </div>
              <div className="mb-2"><span className="font-bold">Priority:</span> {escalations[selectedIdx].priority || 'N/A'}</div>
              <div className="mb-2"><span className="font-bold">Date:</span> {escalations[selectedIdx].created_at ? new Date(escalations[selectedIdx].created_at).toLocaleString() : 'N/A'}</div>
              <div className="mb-2"><span className="font-bold">Description:</span></div>
              <div className="p-3 bg-gray-50 rounded text-sm">{escalations[selectedIdx].issue_description || 'N/A'}</div>
              {escalations[selectedIdx].assigned_to && (
                <div className="mt-2 text-sm text-gray-600">
                  <span className="font-bold">Assigned to:</span> {escalations[selectedIdx].assigned_to}
                </div>
              )}
            </div>
            
            {/* Thread bubbles */}
            <div className="flex-1 flex flex-col justify-end overflow-y-auto mb-4">
              <div className="space-y-4">
                {/* HR Reply */}
                {escalations[selectedIdx].hr_reply && (
                  <div className="flex justify-start">
                    <div className="bg-blue-100 border border-blue-300 rounded-2xl px-4 py-3 max-w-2xl shadow-sm">
                      <div className="text-xs text-blue-600 font-medium mb-1">HR Response</div>
                      <div className="text-gray-800 whitespace-pre-line">{escalations[selectedIdx].hr_reply}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {escalations[selectedIdx].hr_reply_at ? new Date(escalations[selectedIdx].hr_reply_at).toLocaleString() : ''}
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Employee Reply */}
                {escalations[selectedIdx].employee_reply && (
                  <div className="flex justify-end">
                    <div className="bg-green-100 border border-green-300 rounded-2xl px-4 py-3 max-w-2xl shadow-sm">
                      <div className="text-xs text-green-600 font-medium mb-1">Your Response</div>
                      <div className="text-gray-800 whitespace-pre-line">{escalations[selectedIdx].employee_reply}</div>
                      <div className="text-xs text-gray-500 mt-1 text-right">
                        {escalations[selectedIdx].employee_reply_at ? new Date(escalations[selectedIdx].employee_reply_at).toLocaleString() : ''}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {/* Follow-up input */}
            <div className="mt-auto flex gap-2">
              <input
                type="text"
                className="flex-1 border rounded px-3 py-2"
                placeholder="Type a reply to HR..."
                value={followUpMsg}
                onChange={e => setFollowUpMsg(e.target.value)}
                onKeyDown={e => { if (e.key === 'Enter' && !sendingReply) handleFollowUpSend(); }}
                disabled={sendingReply}
              />
              <button
                className={`px-4 py-2 rounded text-white ${sendingReply ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}
                onClick={handleFollowUpSend}
                disabled={sendingReply}
              >
                {sendingReply ? 'Sending...' : 'Send'}
              </button>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-400">Select an escalation to view details</div>
        )}
      </div>
    </div>
  );
};

export default EscalationList; 