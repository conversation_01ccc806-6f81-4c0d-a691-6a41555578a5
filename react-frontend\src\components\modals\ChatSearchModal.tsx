import React, { useState, useEffect, useRef } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChatSession } from '@/types';

interface ChatSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  chatSessions: ChatSession[];
  onSelectChat: (sessionId: string) => void;
}

const ChatSearchModal: React.FC<ChatSearchModalProps> = ({ isOpen, onClose, chatSessions, onSelectChat }) => {
  const [query, setQuery] = useState('');
  const [filtered, setFiltered] = useState<ChatSession[]>(chatSessions);
  const [highlighted, setHighlighted] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen) {
      setQuery('');
      setFiltered(chatSessions);
      setHighlighted(0);
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen, chatSessions]);

  useEffect(() => {
    const q = query.trim().toLowerCase();
    setFiltered(
      q
        ? chatSessions.filter((c) => c.title.toLowerCase().includes(q))
        : chatSessions
    );
    setHighlighted(0);
  }, [query, chatSessions]);

  // Keyboard navigation
  useEffect(() => {
    if (!isOpen) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
      if (e.key === 'ArrowDown') {
        setHighlighted((h) => Math.min(h + 1, filtered.length - 1));
        e.preventDefault();
      }
      if (e.key === 'ArrowUp') {
        setHighlighted((h) => Math.max(h - 1, 0));
        e.preventDefault();
      }
      if (e.key === 'Enter' && filtered[highlighted]) {
        onSelectChat(filtered[highlighted].id);
        onClose();
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, filtered, highlighted, onClose, onSelectChat]);

  // Click outside to close
  useEffect(() => {
    if (!isOpen) return;
    const handleClick = (e: MouseEvent) => {
      if (
        resultsRef.current &&
        !resultsRef.current.contains(e.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(e.target as Node)
      ) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [isOpen, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-start justify-center bg-black/30 backdrop-blur-sm"
          aria-modal="true"
          role="dialog"
        >
          <motion.div
            initial={{ y: -40, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -40, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
            className="mt-32 w-full max-w-2xl rounded-2xl bg-white dark:bg-zinc-900 shadow-2xl p-6 border border-zinc-200 dark:border-zinc-700"
            ref={resultsRef}
          >
            <div className="flex items-center mb-4">
              <input
                ref={inputRef}
                type="text"
                className="w-full px-4 py-3 rounded-lg border border-zinc-300 dark:border-zinc-700 bg-zinc-50 dark:bg-zinc-800 text-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
                placeholder="Search chats..."
                value={query}
                onChange={e => setQuery(e.target.value)}
                autoFocus
              />
              <button
                onClick={onClose}
                className="ml-2 px-3 py-2 rounded-lg bg-zinc-200 dark:bg-zinc-700 hover:bg-zinc-300 dark:hover:bg-zinc-600 text-zinc-700 dark:text-zinc-200"
                aria-label="Close search"
              >
                ×
              </button>
            </div>
            <div className="max-h-96 overflow-y-auto divide-y divide-zinc-100 dark:divide-zinc-800">
              {filtered.length === 0 ? (
                <div className="py-8 text-center text-zinc-500">No chats found.</div>
              ) : (
                filtered.map((chat, idx) => (
                  <button
                    key={chat.id}
                    className={`w-full text-left px-4 py-3 rounded-lg flex items-center gap-2 transition-colors ${
                      idx === highlighted
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100'
                        : 'hover:bg-zinc-100 dark:hover:bg-zinc-800 text-zinc-800 dark:text-zinc-100'
                    }`}
                    onClick={() => {
                      onSelectChat(chat.id);
                      onClose();
                    }}
                    onMouseEnter={() => setHighlighted(idx)}
                  >
                    <span className="truncate font-medium">{chat.title}</span>
                    <span className="ml-auto text-xs text-zinc-400">{chat.updatedAt.toLocaleString?.() || ''}</span>
                  </button>
                ))
              )}
            </div>
            <div className="mt-4 text-xs text-zinc-500 dark:text-zinc-400 text-right">
              Press <kbd className="px-1 py-0.5 bg-zinc-200 dark:bg-zinc-700 rounded">Esc</kbd> to close
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ChatSearchModal; 