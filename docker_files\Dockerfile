# --- Stage 1: Build React frontend ---
FROM node:20-alpine AS frontend-builder
WORKDIR /app/react-frontend
COPY react-frontend/package.json react-frontend/package-lock.json ./
RUN npm ci
COPY react-frontend/ ./
RUN npm run build

# --- Stage 2: Build Python backend ---
FROM python:3.11-slim AS backend
WORKDIR /app

# Install system dependencies (if needed)
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy Python requirements and install
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy backend code
COPY app.py ./
COPY src/ ./src/

# Copy React build from previous stage
COPY --from=frontend-builder /app/react-frontend/build ./react-frontend/build

# Expose port (Flask default)
EXPOSE 5000

# Set environment variables
ENV FLASK_APP=app.py
ENV FLASK_ENV=production

# Run Flask app
CMD ["python", "app.py"] 