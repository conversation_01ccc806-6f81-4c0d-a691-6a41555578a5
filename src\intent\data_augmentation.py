"""
Data augmentation for intent classification training.
"""

import random
import re
from typing import List, Dict, Any
import logging

# Try to import NLTK, but don't fail if not available
try:
    import nltk
    from nltk.corpus import wordnet
    from nltk.tokenize import word_tokenize
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False
    print("Warning: NLTK not available. Some augmentation features will be disabled.")

logger = logging.getLogger(__name__)


class DataAugmenter:
    """
    Data augmentation for intent classification.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_augmentations = config.get('max_augmentations_per_sample', 2)
        
        # Download required NLTK data if available
        if NLTK_AVAILABLE:
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                try:
                    nltk.download('punkt')
                except:
                    pass
            
            try:
                nltk.data.find('corpora/wordnet')
            except LookupError:
                try:
                    nltk.download('wordnet')
                except:
                    pass
        
        # HR-specific templates
        self.hr_templates = {
            'leave': [
                "How do I {action} {leave_type}?",
                "What's the process for {action} {leave_type}?",
                "I need to {action} {leave_type}",
                "Can I {action} {leave_type}?",
                "What are the steps to {action} {leave_type}?"
            ],
            'benefits': [
                "Tell me about {benefit_type}",
                "What {benefit_type} are available?",
                "How do I access {benefit_type}?",
                "I want to know about {benefit_type}",
                "What's covered under {benefit_type}?"
            ],
            'compensation': [
                "How is my {comp_type} calculated?",
                "What's the {comp_type} process?",
                "I have questions about my {comp_type}",
                "Can you explain {comp_type}?",
                "What affects my {comp_type}?"
            ],
            'policy': [
                "What's the policy on {policy_topic}?",
                "Are there guidelines for {policy_topic}?",
                "I need to know about {policy_topic}",
                "What are the rules for {policy_topic}?",
                "Is there a policy about {policy_topic}?"
            ]
        }
        
        # HR-specific synonyms
        self.hr_synonyms = {
            'leave': ['time off', 'vacation', 'sick leave', 'personal time', 'absence'],
            'benefits': ['insurance', 'coverage', 'perks', 'advantages', 'entitlements'],
            'salary': ['pay', 'compensation', 'wages', 'earnings', 'income'],
            'policy': ['guidelines', 'rules', 'procedures', 'regulations', 'standards'],
            'request': ['apply for', 'ask for', 'submit', 'file', 'put in for'],
            'process': ['procedure', 'steps', 'method', 'approach', 'workflow'],
            'information': ['details', 'info', 'data', 'facts', 'particulars'],
            'help': ['assist', 'support', 'aid', 'guide', 'facilitate']
        }
    
    def augment(self, text: str) -> List[str]:
        """
        Generate augmented versions of the input text.
        
        Args:
            text: Input text to augment
            
        Returns:
            List of augmented texts
        """
        augmentations = []
        
        # Apply different augmentation techniques
        if self.config.get('paraphrasing', True):
            paraphrased = self._paraphrase(text)
            augmentations.extend(paraphrased)
        
        if self.config.get('templating', True):
            templated = self._apply_templates(text)
            augmentations.extend(templated)
        
        if self.config.get('synonym_replacement', True):
            synonym_replaced = self._replace_synonyms(text)
            augmentations.extend(synonym_replaced)
        
        # Limit number of augmentations
        if len(augmentations) > self.max_augmentations:
            augmentations = random.sample(augmentations, self.max_augmentations)
        
        return augmentations
    
    def _paraphrase(self, text: str) -> List[str]:
        """Generate paraphrased versions of the text."""
        paraphrases = []
        
        # Simple paraphrasing techniques
        paraphrases.append(self._change_voice(text))
        paraphrases.append(self._add_filler_words(text))
        paraphrases.append(self._change_question_format(text))
        
        # Remove None values
        paraphrases = [p for p in paraphrases if p is not None and p != text]
        
        return paraphrases[:2]  # Limit to 2 paraphrases
    
    def _change_voice(self, text: str) -> str:
        """Change active/passive voice."""
        # Simple voice change patterns
        patterns = [
            (r"I need to (.+)", r"I want to \1"),
            (r"I want to (.+)", r"I need to \1"),
            (r"Can you (.+)", r"Could you \1"),
            (r"Could you (.+)", r"Can you \1"),
            (r"How do I (.+)", r"What's the way to \1"),
            (r"What's the process for (.+)", r"How do I \1"),
        ]
        
        for pattern, replacement in patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        
        return text
    
    def _add_filler_words(self, text: str) -> str:
        """Add filler words to make text more natural."""
        fillers = ['please', 'kindly', 'actually', 'basically', 'essentially']
        
        if random.random() < 0.3:  # 30% chance
            filler = random.choice(fillers)
            if text.startswith('How') or text.startswith('What') or text.startswith('Can'):
                return f"{filler.capitalize()}, {text.lower()}"
            else:
                return f"{filler.capitalize()} {text}"
        
        return text
    
    def _change_question_format(self, text: str) -> str:
        """Change question format."""
        if text.endswith('?'):
            # Change question type
            if text.startswith('How'):
                return text.replace('How', 'What is the process to', 1).replace('?', '?')
            elif text.startswith('What'):
                return text.replace('What', 'How do I', 1)
            elif text.startswith('Can'):
                return text.replace('Can', 'Is it possible to', 1)
        
        return text
    
    def _apply_templates(self, text: str) -> List[str]:
        """Apply HR-specific templates."""
        templated = []
        
        # Detect intent category from text
        intent_category = self._detect_intent_category(text)
        
        if intent_category in self.hr_templates:
            templates = self.hr_templates[intent_category]
            
            # Extract key terms from text
            key_terms = self._extract_key_terms(text, intent_category)
            
            # Apply templates
            for template in random.sample(templates, min(2, len(templates))):
                try:
                    templated_text = template.format(**key_terms)
                    if templated_text != text:
                        templated.append(templated_text)
                except KeyError:
                    continue
        
        return templated
    
    def _detect_intent_category(self, text: str) -> str:
        """Detect the intent category from text."""
        text_lower = text.lower()
        
        if any(word in text_lower for word in ['leave', 'vacation', 'sick', 'time off', 'absence']):
            return 'leave'
        elif any(word in text_lower for word in ['benefit', 'insurance', 'coverage', 'health', 'dental']):
            return 'benefits'
        elif any(word in text_lower for word in ['salary', 'pay', 'compensation', 'bonus', 'raise']):
            return 'compensation'
        elif any(word in text_lower for word in ['policy', 'rule', 'guideline', 'procedure']):
            return 'policy'
        else:
            return 'general'
    
    def _extract_key_terms(self, text: str, category: str) -> Dict[str, str]:
        """Extract key terms for template filling."""
        text_lower = text.lower()
        terms = {}
        
        if category == 'leave':
            leave_types = ['annual leave', 'sick leave', 'personal leave', 'vacation', 'time off']
            actions = ['request', 'apply for', 'take', 'get', 'schedule']
            
            for leave_type in leave_types:
                if leave_type in text_lower:
                    terms['leave_type'] = leave_type
                    break
            
            for action in actions:
                if action in text_lower:
                    terms['action'] = action
                    break
            
            if 'leave_type' not in terms:
                terms['leave_type'] = 'leave'
            if 'action' not in terms:
                terms['action'] = 'request'
        
        elif category == 'benefits':
            benefit_types = ['health insurance', 'dental coverage', 'vision insurance', 'retirement plan', 'benefits']
            
            for benefit_type in benefit_types:
                if benefit_type in text_lower:
                    terms['benefit_type'] = benefit_type
                    break
            
            if 'benefit_type' not in terms:
                terms['benefit_type'] = 'benefits'
        
        elif category == 'compensation':
            comp_types = ['salary', 'pay', 'compensation', 'bonus', 'raise']
            
            for comp_type in comp_types:
                if comp_type in text_lower:
                    terms['comp_type'] = comp_type
                    break
            
            if 'comp_type' not in terms:
                terms['comp_type'] = 'compensation'
        
        elif category == 'policy':
            policy_topics = ['remote work', 'dress code', 'attendance', 'conduct', 'policy']
            
            for topic in policy_topics:
                if topic in text_lower:
                    terms['policy_topic'] = topic
                    break
            
            if 'policy_topic' not in terms:
                terms['policy_topic'] = 'company policies'
        
        return terms
    
    def _replace_synonyms(self, text: str) -> List[str]:
        """Replace words with synonyms."""
        synonyms = []
        
        # Simple tokenization without NLTK
        tokens = text.split()
        
        # Find words that have synonyms
        replaceable_words = []
        for i, token in enumerate(tokens):
            if token.lower() in self.hr_synonyms:
                replaceable_words.append((i, token))
        
        # Generate synonym replacements
        for _ in range(min(2, len(replaceable_words))):
            if not replaceable_words:
                break
            
            # Choose a random word to replace
            idx, word = random.choice(replaceable_words)
            replaceable_words.remove((idx, word))
            
            # Get synonyms
            word_synonyms = self.hr_synonyms[word.lower()]
            synonym = random.choice(word_synonyms)
            
            # Create new text
            new_tokens = tokens.copy()
            new_tokens[idx] = synonym
            new_text = ' '.join(new_tokens)
            
            if new_text != text:
                synonyms.append(new_text)
        
        return synonyms
    
    def _backtranslation(self, text: str) -> str:
        """Backtranslation augmentation (placeholder for future implementation)."""
        # This would require translation models
        # For now, return the original text
        return text


class HardNegativeMiner:
    """
    Hard negative mining for contrastive learning.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mining_ratio = config.get('mining_ratio', 0.2)
        self.similarity_threshold = config.get('similarity_threshold', 0.7)
    
    def mine_hard_negatives(self, embeddings: List, labels: List, queries: List[str]) -> List[int]:
        """
        Mine hard negative samples for training.
        
        Args:
            embeddings: List of embeddings
            labels: List of labels
            queries: List of original queries
            
        Returns:
            List of indices for hard negative samples
        """
        hard_negatives = []
        
        # Calculate pairwise similarities
        similarities = self._calculate_similarities(embeddings)
        
        # Find hard negatives (high similarity but different labels)
        for i in range(len(embeddings)):
            for j in range(i + 1, len(embeddings)):
                if labels[i] != labels[j]:  # Different labels
                    similarity = similarities[i][j]
                    
                    if similarity > self.similarity_threshold:
                        # This is a hard negative pair
                        hard_negatives.extend([i, j])
        
        # Limit number of hard negatives
        max_hard_negatives = int(len(embeddings) * self.mining_ratio)
        if len(hard_negatives) > max_hard_negatives:
            hard_negatives = random.sample(hard_negatives, max_hard_negatives)
        
        return list(set(hard_negatives))  # Remove duplicates
    
    def _calculate_similarities(self, embeddings: List) -> List[List[float]]:
        """Calculate pairwise similarities between embeddings."""
        import numpy as np
        from sklearn.metrics.pairwise import cosine_similarity
        
        # Convert to numpy array
        embeddings_array = np.array(embeddings)
        
        # Calculate cosine similarities
        similarities = cosine_similarity(embeddings_array)
        
        return similarities.tolist() 