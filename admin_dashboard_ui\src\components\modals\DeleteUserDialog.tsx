import React, { useState } from 'react';
import { Button } from '../ui/button';
import toast from 'react-hot-toast';

interface DeleteUserDialogProps {
  user: { id: number; full_name: string; email: string };
  onClose: () => void;
  onDelete: (userId: number) => void;
}

const DeleteUserDialog: React.FC<DeleteUserDialogProps> = ({ user, onClose, onDelete }) => {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    setLoading(true);
    try {
      await onDelete(user.id);
      // Removed toast.success here to prevent duplicate popups
    } catch (e: any) {
      toast.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
      <div className="bg-white dark:bg-gray-900 rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-bold mb-4">Remove Admin User</h3>
        <p>Are you sure you want to remove <span className="font-semibold">{user.full_name}</span> ({user.email})?</p>
        <div className="flex justify-end gap-2 mt-6">
          <Button type="button" variant="secondary" onClick={onClose}>Cancel</Button>
          <Button type="button" variant="destructive" onClick={handleDelete} disabled={loading}>
            {loading ? 'Removing...' : 'Remove'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DeleteUserDialog; 