import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { getChatLogs } from '@/services/api';

type ChatLog = {
  user_query: string;
  assistant_response: string;
  language: string;
  timestamp: string;
};

const ChatLogs = () => {
  const [chatLogs, setChatLogs] = useState<ChatLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    getChatLogs()
      .then((res: { data: ChatLog[] }) => {
        setChatLogs(res.data);
      })
      .catch(() => setError('Failed to load chat logs'))
      .finally(() => setLoading(false));
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Chat Logs</h1>
          <p className="text-muted-foreground">
            Review detailed chat conversations.
          </p>
        </div>
      </div>

      <Card className="rounded-2xl border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader>
          <CardTitle>All Conversations</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <Skeleton className="h-64 w-full rounded-lg" />
          ) : error ? (
            <div className="text-red-500 p-4">{error}</div>
          ) : chatLogs.length > 0 ? (
            <div className="space-y-4 max-h-[600px] overflow-y-auto pr-4">
              {chatLogs.map((log, index) => (
                <div key={index} className="border-b pb-4 last:border-b-0">
                  <p className="text-sm text-muted-foreground"><strong>Timestamp:</strong> {new Date(log.timestamp).toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground"><strong>Language:</strong> {log.language}</p>
                  <p className="mt-2"><strong>User:</strong> {log.user_query}</p>
                  <p className="mt-1"><strong>Assistant:</strong> {log.assistant_response}</p>
                </div>
              ))}
            </div>
          ) : (
            <p>No chat logs available.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ChatLogs;