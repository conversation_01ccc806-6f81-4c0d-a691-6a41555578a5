import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DateRangePicker } from "@/components/ui/date-range-picker"; // Assume stub
import { Select } from "@/components/ui/select"; // Assume stub
import { Skeleton } from "@/components/ui/skeleton";
import api from "@/services/api";

const PAGE_SIZE = 10;
const ENTITY_TYPES = ["Person", "Date", "Amount", "Department", "Other"];
const INTENTS = ["Leave Balance", "Payslip", "Attendance", "Policy", "Holiday", "Other"];

const columns = [
  { key: "query", label: "Query" },
  { key: "entities", label: "Current Entities" },
  { key: "edit_tags", label: "Edit Tags" },
  { key: "intent", label: "Intent" },
  { key: "action", label: "Action" },
];

const NERIntentTrainer = () => {
  const [dateRange, setDateRange] = useState("7d");
  const [intentFilter, setIntentFilter] = useState("All");
  const [entityType, setEntityType] = useState("All");
  const [search, setSearch] = useState("");
  const [page, setPage] = useState(1);
  const [data, setData] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editedRows, setEditedRows] = useState<{ [id: string]: any }>({});
  const [selectedRows, setSelectedRows] = useState<{ [id: string]: boolean }>({});

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = [
      `range=${dateRange}`,
      intentFilter !== "All" ? `intent=${encodeURIComponent(intentFilter)}` : null,
      entityType !== "All" ? `entityType=${encodeURIComponent(entityType)}` : null,
      search ? `query=${encodeURIComponent(search)}` : null,
      `page=${page}`,
      `pageSize=${PAGE_SIZE}`,
    ]
      .filter(Boolean)
      .join("&");
    api
      .get(`/training/ner-intent-trainer?${params}`)
      .then((res) => {
        setData(res.data.queries || []);
        setTotal(res.data.total || 0);
      })
      .catch((err) => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [dateRange, intentFilter, entityType, search, page]);

  const handleEntityChange = (rowId: string, entityIdx: number, newType: string) => {
    setEditedRows((prev) => {
      const prevRow = prev[rowId] || {};
      const newEntities = [...(prevRow.entities || data.find(r => r.id === rowId)?.entities || [])];
      newEntities[entityIdx] = { ...newEntities[entityIdx], type: newType };
      return { ...prev, [rowId]: { ...prevRow, entities: newEntities } };
    });
  };

  const handleIntentChange = (rowId: string, newIntent: string) => {
    setEditedRows((prev) => ({ ...prev, [rowId]: { ...prev[rowId], intent: newIntent } }));
  };

  const handleSave = (rowId: string) => {
    // Mock API call
    alert(`Saved edits for row ${rowId} (mock)`);
  };

  const handleFlag = (rowId: string) => {
    // Mock API call
    alert(`Flagged row ${rowId} for retraining (mock)`);
  };

  const handleSelectRow = (rowId: string) => {
    setSelectedRows((prev) => ({ ...prev, [rowId]: !prev[rowId] }));
  };

  const handleExport = () => {
    alert("Export selected rows (mock)");
  };

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  const totalPages = Math.ceil(total / PAGE_SIZE);

  return (
    <div className="max-w-7xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle>NER & Intent Trainer</CardTitle>
          <div className="flex flex-wrap gap-2 items-center">
            <Select value={dateRange} onValueChange={setDateRange}>
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="custom">Custom</option>
            </Select>
            <Select value={intentFilter} onValueChange={setIntentFilter}>
              <option value="All">All Intents</option>
              {INTENTS.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
            <Select value={entityType} onValueChange={setEntityType}>
              <option value="All">All Entity Types</option>
              {ENTITY_TYPES.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </Select>
            <Input
              type="text"
              placeholder="Search queries..."
              value={search}
              onChange={e => { setSearch(e.target.value); setPage(1); }}
              className="w-56"
            />
            <Button size="sm" variant="outline" onClick={handleExport}>
              Export Selected
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
              <thead className="bg-muted dark:bg-zinc-800">
                <tr>
                  <th></th>
                  {columns.map(col => (
                    <th key={col.key} className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">
                      {col.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-background dark:bg-zinc-900">
                {data.map((row, idx) => {
                  const edited = editedRows[row.id] || {};
                  const entities = edited.entities || row.entities || [];
                  return (
                    <tr key={row.id || idx} className="border-b border-border dark:border-zinc-800">
                      <td className="px-2 py-2 text-sm">
                        <input
                          type="checkbox"
                          checked={!!selectedRows[row.id]}
                          onChange={() => handleSelectRow(row.id)}
                          aria-label="Select row"
                        />
                      </td>
                      {/* Query with inline entity tags */}
                      <td className="px-4 py-2 text-sm font-mono whitespace-pre-wrap">
                        {entities.length ? (
                          <>
                            {entities.reduce((acc: any[], entity: any, i: number) => {
                              acc.push(
                                <span key={`before-${i}`}>{row.query.slice(i === 0 ? 0 : entities[i - 1].end, entity.start)}</span>
                              );
                              acc.push(
                                <span
                                  key={`entity-${i}`}
                                  className="bg-primary/20 rounded px-1 mx-0.5 text-primary font-semibold"
                                >
                                  {row.query.slice(entity.start, entity.end)}
                                </span>
                              );
                              if (i === entities.length - 1) {
                                acc.push(
                                  <span key={`after-${i}`}>{row.query.slice(entity.end)}</span>
                                );
                              }
                              return acc;
                            }, [])}
                          </>
                        ) : (
                          row.query
                        )}
                      </td>
                      {/* Current Entities with editable type */}
                      <td className="px-4 py-2 text-sm">
                        <div className="flex flex-wrap gap-2">
                          {entities.map((entity: any, i: number) => (
                            <div key={i} className="flex items-center gap-1">
                              <span className="bg-muted rounded px-1 text-xs font-mono">
                                {row.query.slice(entity.start, entity.end)}
                              </span>
                              <Select
                                value={entity.type}
                                onValueChange={val => handleEntityChange(row.id, i, val)}
                              >
                                {ENTITY_TYPES.map(opt => (
                                  <option key={opt} value={opt}>{opt}</option>
                                ))}
                              </Select>
                            </div>
                          ))}
                        </div>
                      </td>
                      {/* Editable intent */}
                      <td className="px-4 py-2 text-sm">
                        <Select
                          value={edited.intent || row.intent}
                          onValueChange={val => handleIntentChange(row.id, val)}
                        >
                          {INTENTS.map(opt => (
                            <option key={opt} value={opt}>{opt}</option>
                          ))}
                        </Select>
                      </td>
                      {/* Actions */}
                      <td className="px-4 py-2 text-sm flex gap-2">
                        <Button size="sm" variant="outline" onClick={() => handleSave(row.id)}>
                          Save
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleFlag(row.id)}>
                          Flag
                        </Button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          {/* Pagination controls */}
          <div className="flex justify-end gap-2 items-center mt-4">
            <Button size="sm" variant="ghost" disabled={page === 1} onClick={() => setPage(page - 1)}>
              Previous
            </Button>
            <span className="text-xs text-muted-foreground">
              Page {page} of {totalPages || 1}
            </span>
            <Button size="sm" variant="ghost" disabled={page === totalPages || totalPages === 0} onClick={() => setPage(page + 1)}>
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default NERIntentTrainer; 