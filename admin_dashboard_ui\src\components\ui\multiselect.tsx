import React from "react";

type MultiSelectProps = {
  value?: string[];
  onChange?: (value: string[]) => void;
  options: { label: string; value: string }[];
  placeholder?: string;
};

export const MultiSelect = React.forwardRef<HTMLSelectElement, MultiSelectProps>(
  ({ value, onChange, options, placeholder, ...props }, ref) => (
    <select
      ref={ref}
      multiple
      value={value}
      onChange={e => {
        const selected = Array.from(e.target.selectedOptions, option => option.value);
        onChange?.(selected);
      }}
      {...props}
    >
      {placeholder && <option disabled value="">{placeholder}</option>}
      {options.map(opt => (
        <option key={opt.value} value={opt.value}>{opt.label}</option>
      ))}
    </select>
  )
);
MultiSelect.displayName = 'MultiSelect'; 