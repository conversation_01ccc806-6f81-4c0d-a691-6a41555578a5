import { create } from "zustand";

type SidebarState = {
  sidebarOpen: boolean;
  toggleSidebar: () => void;
};

export const useSidebarStore = create<SidebarState>((set: (fn: (state: SidebarState) => SidebarState | Partial<SidebarState>) => void) => ({
  sidebarOpen: true, // Sidebar is now open by default
  toggleSidebar: () => set((state: SidebarState) => ({ sidebarOpen: !state.sidebarOpen })),
})); 