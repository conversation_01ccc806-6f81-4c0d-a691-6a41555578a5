"""
Application Performance Monitoring (APM) System for the Advanced RAG Chatbot.

This module provides comprehensive monitoring capabilities including:
- Performance metrics collection
- Request tracing and profiling
- Error tracking and alerting
- Resource usage monitoring
- Custom business metrics
- Health checks and status reporting
"""

import time
import threading
import psutil
import os
import json
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field, asdict
from collections import defaultdict, deque
from contextlib import contextmanager
import logging
from pathlib import Path
from functools import wraps

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics for a single operation."""
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration_ms(self) -> float:
        """Get duration in milliseconds."""
        return self.duration * 1000


@dataclass
class SystemMetrics:
    """System resource usage metrics."""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_io: Dict[str, float]
    active_threads: int
    open_files: int


@dataclass
class BusinessMetrics:
    """Business-specific metrics."""
    timestamp: float
    total_queries: int
    successful_queries: int
    failed_queries: int
    average_response_time: float
    documents_processed: int
    embeddings_generated: int
    vector_searches: int
    user_sessions: int
    escalations: int


@dataclass
class ErrorEvent:
    """Error event information."""
    timestamp: float
    error_type: str
    error_message: str
    stack_trace: str
    context: Dict[str, Any]
    severity: str = "error"


class MetricsCollector:
    """Collects and manages performance metrics."""
    
    def __init__(self, max_metrics: int = 10000):
        self.max_metrics = max_metrics
        self.metrics: deque = deque(maxlen=max_metrics)
        self.errors: deque = deque(maxlen=max_metrics)
        self._lock = threading.RLock()
    
    def add_metric(self, metric: PerformanceMetrics):
        """Add a performance metric."""
        with self._lock:
            self.metrics.append(metric)
    
    def add_error(self, error: ErrorEvent):
        """Add an error event."""
        with self._lock:
            self.errors.append(error)
    
    def get_metrics(self, operation_name: Optional[str] = None, 
                   time_window: Optional[timedelta] = None) -> List[PerformanceMetrics]:
        """Get metrics filtered by operation name and time window."""
        with self._lock:
            metrics = list(self.metrics)
        
        if operation_name:
            metrics = [m for m in metrics if m.operation_name == operation_name]
        
        if time_window:
            cutoff_time = time.time() - time_window.total_seconds()
            metrics = [m for m in metrics if m.end_time >= cutoff_time]
        
        return metrics
    
    def get_errors(self, error_type: Optional[str] = None,
                  time_window: Optional[timedelta] = None) -> List[ErrorEvent]:
        """Get errors filtered by type and time window."""
        with self._lock:
            errors = list(self.errors)
        
        if error_type:
            errors = [e for e in errors if e.error_type == error_type]
        
        if time_window:
            cutoff_time = time.time() - time_window.total_seconds()
            errors = [e for e in errors if e.timestamp >= cutoff_time]
        
        return errors
    
    def get_statistics(self, operation_name: Optional[str] = None,
                      time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """Get statistical summary of metrics."""
        metrics = self.get_metrics(operation_name, time_window)
        
        if not metrics:
            return {
                "count": 0,
                "success_rate": 0.0,
                "avg_duration": 0.0,
                "min_duration": 0.0,
                "max_duration": 0.0,
                "p95_duration": 0.0,
                "p99_duration": 0.0
            }
        
        durations = [m.duration for m in metrics]
        successful = [m for m in metrics if m.success]
        
        # Calculate percentiles
        sorted_durations = sorted(durations)
        p95_index = int(len(sorted_durations) * 0.95)
        p99_index = int(len(sorted_durations) * 0.99)
        
        return {
            "count": len(metrics),
            "success_rate": len(successful) / len(metrics),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "p95_duration": sorted_durations[p95_index] if p95_index < len(sorted_durations) else max(durations),
            "p99_duration": sorted_durations[p99_index] if p99_index < len(sorted_durations) else max(durations)
        }


class SystemMonitor:
    """Monitors system resources."""
    
    def __init__(self):
        self.last_network_io = psutil.net_io_counters()
        self.last_network_time = time.time()
    
    def collect_metrics(self) -> SystemMetrics:
        """Collect current system metrics."""
        # CPU and memory
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        
        # Disk usage
        disk_usage = psutil.disk_usage('/')
        
        # Network I/O
        current_network = psutil.net_io_counters()
        current_time = time.time()
        time_diff = current_time - self.last_network_time
        
        network_io = {
            "bytes_sent_per_sec": (current_network.bytes_sent - self.last_network_io.bytes_sent) / time_diff,
            "bytes_recv_per_sec": (current_network.bytes_recv - self.last_network_io.bytes_recv) / time_diff
        }
        
        self.last_network_io = current_network
        self.last_network_time = current_time
        
        # Process-specific metrics
        process = psutil.Process()
        open_files = len(process.open_files())
        active_threads = process.num_threads()
        
        return SystemMetrics(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / (1024 * 1024),
            memory_available_mb=memory.available / (1024 * 1024),
            disk_usage_percent=disk_usage.percent,
            network_io=network_io,
            active_threads=active_threads,
            open_files=open_files
        )


class BusinessMetricsCollector:
    """Collects business-specific metrics."""
    
    def __init__(self):
        self.metrics = defaultdict(int)
        self.response_times = deque(maxlen=1000)
        self._lock = threading.RLock()
    
    def increment_counter(self, metric_name: str, value: int = 1):
        """Increment a counter metric."""
        with self._lock:
            self.metrics[metric_name] += value
    
    def record_response_time(self, response_time: float):
        """Record a response time."""
        with self._lock:
            self.response_times.append(response_time)
    
    def get_metrics(self) -> BusinessMetrics:
        """Get current business metrics."""
        with self._lock:
            avg_response_time = (sum(self.response_times) / len(self.response_times) 
                               if self.response_times else 0.0)
            
            return BusinessMetrics(
                timestamp=time.time(),
                total_queries=self.metrics["total_queries"],
                successful_queries=self.metrics["successful_queries"],
                failed_queries=self.metrics["failed_queries"],
                average_response_time=avg_response_time,
                documents_processed=self.metrics["documents_processed"],
                embeddings_generated=self.metrics["embeddings_generated"],
                vector_searches=self.metrics["vector_searches"],
                user_sessions=self.metrics["user_sessions"],
                escalations=self.metrics["escalations"]
            )


class APM:
    """Main APM system that coordinates all monitoring components."""
    
    def __init__(self, metrics_dir: Optional[Path] = None):
        self.metrics_dir = metrics_dir or Path("data/metrics")
        self.metrics_dir.mkdir(parents=True, exist_ok=True)
        
        self.collector = MetricsCollector()
        self.system_monitor = SystemMonitor()
        self.business_collector = BusinessMetricsCollector()
        
        self.health_checks: Dict[str, Callable] = {}
        self.alert_handlers: List[Callable] = []
        self.monitoring_enabled = False  # <--- DISABLE monitoring
        
        # Add health monitoring with reduced log frequency
        self._last_health_log = time.time()
        self.alerts = []
        
        # Start background monitoring
        self._monitoring_thread = None
        self._stop_monitoring = threading.Event()
        # self._start_background_monitoring()  # <--- DISABLE background monitoring
        
        self._max_metrics_file_size = 5 * 1024 * 1024  # 5MB

    def enable_monitoring(self):
        self.monitoring_enabled = False  # Always keep disabled

    def _start_background_monitoring(self):
        pass  # Do nothing

    def _monitor_loop(self):
        pass  # Do nothing

    def _collect_system_metrics(self):
        pass  # Do nothing

    def _save_metrics(self, metric_type: str, data: Dict[str, Any]):
        pass  # Do nothing

    def _run_health_checks(self):
        pass  # Do nothing

    def _check_alerts(self):
        pass  # Do nothing

    def _trigger_alert(self, alert_type: str, message: str):
        pass  # Do nothing

    @contextmanager
    def trace_operation(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        """Context manager for tracing operations."""
        if not self.monitoring_enabled:
            yield
            return
        
        start_time = time.time()
        success = True
        error_message = None
        
        try:
            yield
        except Exception as e:
            success = False
            error_message = str(e)
            raise
        finally:
            end_time = time.time()
            duration = end_time - start_time
            
            metric = PerformanceMetrics(
                operation_name=operation_name,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=success,
                error_message=error_message,
                metadata=metadata or {}
            )
            
            self.collector.add_metric(metric)
            
            # Record business metrics
            if operation_name == "query_processing":
                self.business_collector.increment_counter("total_queries")
                if success:
                    self.business_collector.increment_counter("successful_queries")
                else:
                    self.business_collector.increment_counter("failed_queries")
                self.business_collector.record_response_time(duration)

    def record_error(self, error_type: str, error_message: str, 
                    context: Optional[Dict[str, Any]] = None):
        """Record an error event."""
        if not self.monitoring_enabled:
            return
        
        error = ErrorEvent(
            timestamp=time.time(),
            error_type=error_type,
            error_message=error_message,
            stack_trace=traceback.format_exc(),
            context=context or {}
        )
        
        self.collector.add_error(error)

    def add_health_check(self, name: str, check_func: Callable):
        """Add a health check function."""
        self.health_checks[name] = check_func

    def add_alert_handler(self, handler: Callable):
        """Add an alert handler function."""
        self.alert_handlers.append(handler)

    def get_performance_summary(self, time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """Get a summary of performance metrics."""
        return {
            "system": asdict(self.system_monitor.collect_metrics()),
            "business": asdict(self.business_collector.get_metrics()),
            "performance": {
                "query_processing": self.collector.get_statistics("query_processing", time_window),
                "document_processing": self.collector.get_statistics("document_processing", time_window),
                "vector_search": self.collector.get_statistics("vector_search", time_window),
                "embedding_generation": self.collector.get_statistics("embedding_generation", time_window)
            },
            "errors": {
                "recent_errors": len(self.collector.get_errors(time_window=timedelta(hours=1))),
                "error_types": self._get_error_type_counts(time_window)
            }
        }

    def _get_error_type_counts(self, time_window: Optional[timedelta] = None) -> Dict[str, int]:
        """Get counts of error types."""
        errors = self.collector.get_errors(time_window=time_window)
        counts = defaultdict(int)
        for error in errors:
            counts[error.error_type] += 1
        return dict(counts)

    def shutdown(self):
        """Shutdown the APM system."""
        self._stop_monitoring.set()
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._monitoring_thread.join(timeout=5)

    def _health_monitor_loop(self):
        """Monitor system health and log status with reduced frequency."""
        while True:
            try:
                # Get health status
                health_data = {
                    "queue_size": 0,
                    "queue_utilization": 0.0,
                    "messages_processed": 0,
                    "messages_dropped": 0,
                    "error_rate": 0.0,
                    "errors_count": 0,
                    "last_error": None,
                    "uptime_seconds": time.time() - self._last_health_log,
                    "circuit_breaker_state": "CLOSED",
                    "status": "HEALTHY",
                    "timestamp": datetime.now().isoformat()
                }
                
                # Only log healthy status every 60 seconds to reduce spam
                if time.time() - self._last_health_log > 60:
                    logger.info(f"[HEALTH] {json.dumps(health_data)}")
                    self._last_health_log = time.time()
                    
                time.sleep(6)  # Check every 6 seconds
            except Exception as e:
                logger.error(f"Health monitor error: {e}")
                time.sleep(6)


# Global APM instance
_apm_instance: Optional[APM] = None


def get_apm() -> APM:
    """Get the global APM instance."""
    global _apm_instance
    if _apm_instance is None:
        _apm_instance = APM()
    return _apm_instance


def trace_operation(operation_name: str, metadata: Optional[Dict[str, Any]] = None):
    """Decorator for tracing operations."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            apm = get_apm()
            with apm.trace_operation(operation_name, metadata):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def record_error(error_type: str, error_message: str, context: Optional[Dict[str, Any]] = None):
    """Record an error event."""
    apm = get_apm()
    apm.record_error(error_type, error_message, context)


# Predefined health checks
def check_database_health():
    """Health check for database connectivity."""
    try:
        # Add your database health check logic here
        return {"healthy": True, "message": "Database is healthy"}
    except Exception as e:
        return {"healthy": False, "message": f"Database error: {e}"}


def check_vector_store_health():
    """Health check for vector store connectivity."""
    try:
        # Add your vector store health check logic here
        return {"healthy": True, "message": "Vector store is healthy"}
    except Exception as e:
        return {"healthy": False, "message": f"Vector store error: {e}"}


def check_external_api_health():
    """Health check for external APIs."""
    try:
        # Add your external API health check logic here
        return {"healthy": True, "message": "External APIs are healthy"}
    except Exception as e:
        return {"healthy": False, "message": f"External API error: {e}"}


# Alert handlers
def log_alert_handler(alert_data: Dict[str, Any]):
    """Log alert handler."""
    logger.warning(f"APM Alert: {alert_data['type']} - {alert_data['message']}")


def email_alert_handler(alert_data: Dict[str, Any]):
    """Email alert handler."""
    # Add your email alert logic here
    logger.info(f"Email alert sent: {alert_data['type']} - {alert_data['message']}")


# Initialize APM with default health checks and alert handlers
def initialize_apm():
    """Initialize the APM system with default configurations."""
    apm = get_apm()
    
    # Add health checks
    apm.add_health_check("database", check_database_health)
    apm.add_health_check("vector_store", check_vector_store_health)
    apm.add_health_check("external_apis", check_external_api_health)
    
    # Add alert handlers
    apm.add_alert_handler(log_alert_handler)
    apm.add_alert_handler(email_alert_handler)
    
    return apm 