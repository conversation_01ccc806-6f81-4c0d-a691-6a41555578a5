import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useReactTable, getCoreRowModel, flexRender, ColumnDef, getSortedRowModel, SortingState } from "@tanstack/react-table";
import api from "@/services/api";
import { motion } from "framer-motion";

const PAGE_SIZE = 10;
// Define the type for a policy drift row
interface PolicyDriftRow {
  question: string;
  current_answer: string;
  new_answer: string;
  drift_score: number;
  last_updated: string;
  action?: string;
  [key: string]: any;
}

const columns: ColumnDef<PolicyDriftRow>[] = [
  { accessorKey: "question", header: "Question" },
  { accessorKey: "current_answer", header: "Current Answer" },
  { accessorKey: "new_answer", header: "New Answer" },
  { accessorKey: "drift_score", header: "Drift Score" },
  { accessorKey: "last_updated", header: "Last Updated" },
  { accessorKey: "action", header: "Action" },
];

const PolicyDrift = () => {
  const [page, setPage] = useState(1);
  const [data, setData] = useState<PolicyDriftRow[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [modal, setModal] = useState<{ open: boolean; row?: any }>({ open: false });

  useEffect(() => {
    setLoading(true);
    setError(null);
    api
      .get(`/ai/policy-drifts?page=${page}&pageSize=${PAGE_SIZE}`)
      .then((res) => {
        setData(res.data.drifts || []);
        setTotal(res.data.total || 0);
      })
      .catch((err) => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [page]);

  const handleReview = (row: any) => {
    setModal({ open: true, row });
  };
  const handleCloseModal = () => setModal({ open: false });

  const table = useReactTable({
    data,
    columns: columns.map(col =>
      col.id === "action"
        ? {
            ...col,
            cell: ({ row }) => (
              <Button size="sm" variant="outline" onClick={() => handleReview(row.original)}>
                Review & Fix
              </Button>
            ),
          }
        : col
    ) as ColumnDef<PolicyDriftRow, any>[],
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: { sorting },
    onSortingChange: setSorting,
    manualSorting: false,
  });

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  const totalPages = Math.ceil(total / PAGE_SIZE);

  return (
    <div className="max-w-6xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Policy Drift Detector</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
              <thead className="bg-muted dark:bg-zinc-800">
                {table.getHeaderGroups().map(headerGroup => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <th
                        key={header.id}
                        className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase cursor-pointer"
                        onClick={header.column.getToggleSortingHandler?.()}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getIsSorted?.() ? (header.column.getIsSorted() === "asc" ? " ▲" : " ▼") : ""}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody className="bg-background dark:bg-zinc-900">
                {table.getRowModel().rows.map(row => {
                  const drift = row.original.drift_score;
                  return (
                    <tr
                      key={row.id}
                      className={`border-b border-border dark:border-zinc-800 ${drift > 0.7 ? "bg-red-100 dark:bg-red-900/30" : ""}`}
                    >
                      {row.getVisibleCells().map(cell => (
                        <td key={cell.id} className="px-4 py-2 text-sm">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </td>
                      ))}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          {/* Pagination controls */}
          <div className="flex justify-end gap-2 items-center mt-4">
            <Button size="sm" variant="ghost" disabled={page === 1} onClick={() => setPage(page - 1)}>
              Previous
            </Button>
            <span className="text-xs text-muted-foreground">
              Page {page} of {totalPages || 1}
            </span>
            <Button size="sm" variant="ghost" disabled={page === totalPages || totalPages === 0} onClick={() => setPage(page + 1)}>
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
      {/* Review & Fix Modal (stub) */}
      {modal.open && (
        <div className="fixed inset-0 bg-black/40 z-50 flex items-center justify-center">
          <div className="bg-background p-6 rounded shadow-lg max-w-lg w-full">
            <div className="font-bold mb-2">Review & Fix</div>
            <div className="mb-4">
              <div className="mb-2 text-xs text-muted-foreground">Question:</div>
              <div className="mb-2 font-mono text-sm">{modal.row?.question}</div>
              <div className="mb-2 text-xs text-muted-foreground">Current Answer:</div>
              <div className="mb-2 font-mono text-sm">{modal.row?.current_answer}</div>
              <div className="mb-2 text-xs text-muted-foreground">New Answer:</div>
              <div className="mb-2 font-mono text-sm">{modal.row?.new_answer}</div>
              <div className="mb-2 text-xs text-muted-foreground">Drift Score:</div>
              <div className="mb-2 font-mono text-sm">{modal.row?.drift_score}</div>
            </div>
            <div className="flex gap-2 justify-end">
              <Button size="sm" variant="outline" onClick={handleCloseModal}>Close</Button>
              <Button size="sm" variant="default" onClick={handleCloseModal}>Mark as Fixed (mock)</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PolicyDrift; 