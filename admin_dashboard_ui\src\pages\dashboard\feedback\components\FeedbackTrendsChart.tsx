import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from "recharts";
import api from "@/services/api";
import { Skeleton } from "@/components/ui/skeleton";

const FeedbackTrendsChart = ({ dateRange }: { dateRange: string }) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    api
      .get(`/feedback/trends?range=${dateRange}`)
      .then((res) => setData(res.data))
      .catch((err) => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [dateRange]);

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  return (
    <div className="h-80 w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 16, right: 16, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#444" />
          <XAxis dataKey="date" stroke="#888" />
          <YAxis stroke="#888" domain={[0, 100]} tickFormatter={v => `${v}%`} />
          <Tooltip formatter={(value: number) => `${value}%`} />
          <Legend />
          <Bar dataKey="notHelpfulPercent" fill="#ef4444" name="% Not Helpful" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default FeedbackTrendsChart; 