import React, { useState, FormEvent } from "react";
import { API_BASE_URL } from "../apiConfig";

interface LoginPageProps {
  onLogin?: () => void;
}

export function LoginPage({ onLogin = () => {} }: LoginPageProps) {
  // Form state
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [twoFACode, setTwoFACode] = useState<string>("");
  // UI state
  const [step, setStep] = useState<"login" | "2fa">("login");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");

  // Handles the initial login attempt (email + password)
  const handleLogin = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");
    setLoading(true);
    try {
      const res = await fetch(`${API_BASE_URL}/admin/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      if (res.status === 401 || res.status === 403) {
        setError("Access denied. Only authorized admin users can log in.");
      } else if (res.status === 500) {
        setError("Server error. Please try again later.");
      } else {
        const data = await res.json();
        if (data.message && data.message.toLowerCase().includes("2fa code required")) {
          setStep("2fa");
          setError("2FA code required. Please enter your code.");
        } else if (data.success) {
          setError("");
          onLogin();
        } else {
          setError(data.message || "Login failed. Please try again.");
        }
      }
    } catch (err: any) {
      setError(err.message || "Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handles the 2FA code submission
  const handle2FASubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");
    setLoading(true);
    try {
      const res = await fetch(`${API_BASE_URL}/admin/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password, twoFACode }),
      });
      if (res.status === 401 || res.status === 403) {
        setError("Access denied. Only authorized admin users can log in.");
      } else if (res.status === 500) {
        setError("Server error. Please try again later.");
      } else {
        const data = await res.json();
        if (data.success) {
          setError("");
          onLogin();
        } else if (data.message && data.message.toLowerCase().includes("2fa")) {
          setStep("2fa"); // Always stay in 2FA step if 2FA error
          setError(data.message || "Invalid 2FA code. Please try again.");
        } else {
          setError(data.message || "Login failed. Please try again.");
        }
      }
    } catch (err: any) {
      setError(err.message || "Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-200/30 to-neutral-900 p-2 sm:p-4">
      <form
        onSubmit={step === "2fa" ? handle2FASubmit : handleLogin}
        className="bg-neutral-950 shadow-xl rounded-2xl p-6 flex flex-col gap-5 w-full max-w-xs sm:max-w-sm md:max-w-md border border-neutral-800"
        aria-label="Admin Login Form"
        autoComplete="off"
      >
        <div className="text-3xl font-extrabold text-center bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent mb-1">
          Proxima28 Admin Login
        </div>
        <div className="text-neutral-400 text-center mb-2 text-base">
          {step === "login"
            ? "Sign in to your admin account"
            : "Enter your 2FA code to continue"}
        </div>
        {/* Debug: Show current step */}
        <div className="text-xs text-yellow-400 text-center font-mono">[DEBUG] Current step: {step}</div>
        <input
          type="email"
          placeholder="Admin Email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="bg-neutral-900 border border-neutral-700 rounded-lg px-3 py-2 text-lg text-neutral-100 focus:outline-none focus:border-blue-400 transition disabled:opacity-60"
          required
          disabled={loading || step === "2fa"}
          aria-label="Admin Email"
          autoComplete="username"
        />
        <input
          type="password"
          placeholder="Password"
          value={password}
          onChange={e => setPassword(e.target.value)}
          className="bg-neutral-900 border border-neutral-700 rounded-lg px-3 py-2 text-lg text-neutral-100 focus:outline-none focus:border-blue-400 transition disabled:opacity-60"
          required
          disabled={loading || step === "2fa"}
          aria-label="Password"
          autoComplete="current-password"
        />
        {step === "2fa" && (
          <div className="flex flex-col gap-2">
            <div className="text-lg text-blue-300 text-center font-bold">2FA code required</div>
            <input
              type="text"
              placeholder="Enter 2FA Code"
              value={twoFACode}
              onChange={e => setTwoFACode(e.target.value)}
              className="bg-neutral-900 border-2 border-blue-500 rounded-xl px-3 py-3 text-xl text-blue-200 focus:outline-none focus:border-blue-700 transition text-center tracking-widest"
              required
              disabled={loading}
              aria-label="2FA Code"
              autoFocus
              autoComplete="one-time-code"
            />
          </div>
        )}
        {error && (
          <div className="text-red-500 text-center font-semibold text-base">
            {error}
          </div>
        )}
        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-600 text-white font-bold rounded-lg py-2 text-lg transition disabled:opacity-60"
          disabled={loading}
          aria-label={step === "2fa" ? "Submit 2FA Code" : "Sign in"}
        >
          {loading
            ? step === "2fa"
              ? "Verifying..."
              : "Signing in..."
            : step === "2fa"
            ? "Submit 2FA Code"
            : "Sign in"}
        </button>
        <div className="text-xs text-neutral-500 text-center mt-1">
          Only admin users are allowed. Unauthorized access is prohibited.
        </div>
      </form>
    </div>
  );
} 