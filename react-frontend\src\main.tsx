import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './styles/globals.css'
// import '../node_modules/@fortawesome/fontawesome-free/css/all.min.css';

// Initialize theme and auth state immediately like original
const initializeApp = () => {
  // Set initial body classes
  document.body.classList.add('not-logged-in');
  document.documentElement.classList.add('not-logged-in');

  // Apply initial theme
  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('hr_assistant_settings');
      return savedSettings ? JSON.parse(savedSettings) : { theme: 'system' };
    } catch (error) {
      console.error('Error loading settings:', error);
      return { theme: 'system' };
    }
  };

  const settings = loadSettings();
  const theme = settings.theme || 'system';

  let actualTheme = theme;
  if (theme === 'system') {
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    actualTheme = prefersDark ? 'dark' : 'light';
  }

  document.body.classList.add(`theme-${actualTheme}`);
};

// Initialize immediately
initializeApp();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
