import React, { useEffect, useState } from "react";
import { Settings } from "lucide-react";
import { API_BASE_URL } from "../apiConfig";

export function OverridesCard() {
  const [overrides, setOverrides] = useState([]);

  useEffect(() => {
    fetch(`${API_BASE_URL}/api/overrides`)
      .then(res => res.json())
      .then(data => setOverrides(data));
  }, []);

  return (
    <div className="bg-neutral-900 rounded-xl shadow-lg p-6 border border-neutral-800">
      <div className="flex items-center mb-4">
        <Settings className="w-6 h-6 text-accent-400 mr-2" />
        <span className="font-semibold text-lg">Manual Overrides</span>
      </div>
      <table className="w-full text-left">
        <thead>
          <tr className="text-neutral-400">
            <th>Pattern</th>
            <th>Response</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {overrides.map((o: any) => (
            <tr key={o.id} className="border-t border-neutral-800">
              <td className="py-2">{o.pattern}</td>
              <td className="py-2">{o.response}</td>
              <td className="py-2">
                <button className="text-accent-400 hover:underline">Edit</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
} 