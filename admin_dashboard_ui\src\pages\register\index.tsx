import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Eye, EyeOff } from "lucide-react";

export default function RegisterPage() {
  const [email, setEmail] = useState("");
  const [fullName, setFullName] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const navigate = useNavigate();

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setLoading(true);
    try {
      const res = await fetch("/api/admin/register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, full_name: fullName, password }),
      });
      if (res.ok) {
        setSuccess("Registration successful! You can now log in.");
        setEmail("");
        setFullName("");
        setPassword("");
        setTimeout(() => {
          navigate("/login");
        }, 1500);
      } else {
        const data = await res.json();
        if (data.detail === "Admin already exists") {
          setError("Admin already exists. Please log in or use a different email.");
        } else {
          setError(data.message || "Registration failed. Try again.");
        }
      }
    } catch (err) {
      setError("Registration failed. Try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-accent-400/30 to-neutral-900">
      {/* Remove floating icon on register page */}
      <form
        onSubmit={handleRegister}
        className="bg-neutral-950 shadow-2xl rounded-2xl p-10 flex flex-col gap-6 w-full max-w-md border border-neutral-800"
      >
        <div className="text-3xl font-extrabold text-center bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent mb-2">
          Admin Registration
        </div>
        <input
          type="text"
          placeholder="Full Name"
          value={fullName}
          onChange={e => setFullName(e.target.value)}
          className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100"
          required
        />
        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100"
          required
        />
        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            placeholder="Password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100 pr-10 w-full"
            required
          />
          <button
            type="button"
            onClick={() => setShowPassword(v => !v)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-200 focus:outline-none"
            tabIndex={-1}
          >
            {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
          </button>
        </div>
        {error && <div className="text-red-500 text-center font-semibold">{error}</div>}
        {success && <div className="text-green-500 text-center font-semibold">{success}</div>}
        <button
          type="submit"
          className="bg-accent-400 hover:bg-accent-600 text-neutral-900 font-bold rounded py-2 text-lg transition"
          disabled={loading}
        >
          {loading ? "Registering..." : "Register"}
        </button>
      </form>
    </div>
  );
} 