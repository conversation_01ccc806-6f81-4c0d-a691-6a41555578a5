# Optimized Intent Classifier Training Requirements
# Production-grade training dependencies

# Core ML libraries
torch>=2.0.0
transformers>=4.30.0
datasets>=2.12.0
accelerate>=0.20.0

# Data processing
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Configuration and logging
pyyaml>=6.0
tqdm>=4.65.0

# Optional: Monitoring and visualization
tensorboard>=2.13.0  # For local monitoring

# Performance optimizations
nvidia-ml-py3>=7.352.0  # For GPU monitoring
psutil>=5.9.0  # For system monitoring

# Data augmentation (if needed)
nlpaug>=1.1.11
textblob>=0.17.1

# PEFT for adapter fine-tuning (optional)
peft>=0.4.0

# Utilities
pathlib2>=2.3.7  # For Python < 3.4
typing-extensions>=4.5.0