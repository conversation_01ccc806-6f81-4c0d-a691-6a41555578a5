import { useContext } from 'react';
import { PermissionsContext } from '../context/PermissionsProvider';

export function usePermissions() {
  const context = useContext(PermissionsContext);
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  const { role, roleLevel, permissions } = context;

  function hasPermission(permissionKey: string): boolean {
    return permissions.includes(permissionKey);
  }

  function canAssignRole(targetRoleLevel: number): boolean {
    // Only allow assigning roles at or below current user's level
    return targetRoleLevel <= roleLevel;
  }

  return {
    role,
    roleLevel,
    permissions,
    hasPermission,
    canAssignRole,
  };
} 