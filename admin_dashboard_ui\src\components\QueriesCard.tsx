import React, { useEffect, useState } from "react";
import { List } from "lucide-react";
import { API_BASE_URL } from "../apiConfig";

export function QueriesCard() {
  const [queries, setQueries] = useState([]);

  useEffect(() => {
    fetch(`${API_BASE_URL}/api/queries`)
      .then(res => res.json())
      .then(data => setQueries(data));
  }, []);

  return (
    <div className="bg-neutral-900 rounded-xl shadow-lg p-6 border border-neutral-800">
      <div className="flex items-center mb-4">
        <List className="w-6 h-6 text-accent-400 mr-2" />
        <span className="font-semibold text-lg">Recent Queries</span>
      </div>
      <ul className="space-y-2">
        {queries.map((q: any) => (
          <li key={q.id} className="flex justify-between items-center bg-neutral-800 rounded p-2">
            <span>{q.text}</span>
            <span className="text-xs text-neutral-400">{q.time}</span>
          </li>
        ))}
      </ul>
    </div>
  );
} 