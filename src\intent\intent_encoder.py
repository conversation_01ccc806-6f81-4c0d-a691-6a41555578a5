"""
Intent classification encoder with PEFT LoRA adapter support.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import <PERSON>Tokenizer, AutoModel, AutoConfig
import numpy as np
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
import json
import os
from pathlib import Path

logger = logging.getLogger(__name__)

# PEFT availability check
try:
    from peft import LoraConfig, get_peft_model, TaskType
    PEFT_AVAILABLE = True
except ImportError:
    PEFT_AVAILABLE = False
    print("Warning: PEFT not available. Adapter fine-tuning will be disabled.")


class AttentionPooling(nn.Module):
    """Attention-based pooling for sentence embeddings."""
    
    def __init__(self, hidden_size: int, attention_size: int = 128):
        super().__init__()
        self.attention = nn.Sequential(
            nn.Linear(hidden_size, attention_size),
            nn.<PERSON>(),
            nn.Linear(attention_size, 1)
        )
    
    def forward(self, hidden_states: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        # Calculate attention weights
        attention_weights = self.attention(hidden_states)  # [batch_size, seq_len, 1]
        
        # Apply attention mask if provided
        if attention_mask is not None:
            # Use a smaller value that works with FP16 and cast to same dtype
            mask_value = torch.tensor(-1e4, dtype=attention_weights.dtype, device=attention_weights.device)
            attention_weights = attention_weights.masked_fill(attention_mask.unsqueeze(-1) == 0, mask_value)
        
        # Apply softmax
        attention_weights = torch.softmax(attention_weights, dim=1)
        
        # Apply attention to hidden states
        attended_output = torch.sum(hidden_states * attention_weights, dim=1)
        
        return attended_output


class ImprovedClassifierHead(nn.Module):
    """Improved classifier head with batch normalization and dropout."""
    
    def __init__(self, input_dim: int, hidden_dims: List[int], num_classes: int, 
                 dropout_rates: List[float], activation: str = "gelu", use_batch_norm: bool = True):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for i, (hidden_dim, dropout_rate) in enumerate(zip(hidden_dims, dropout_rates)):
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            if activation == "gelu":
                layers.append(nn.GELU())
            elif activation == "relu":
                layers.append(nn.ReLU())
            elif activation == "swish":
                layers.append(nn.SiLU())
            
            layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim
        
        # Final classification layer
        layers.append(nn.Linear(prev_dim, num_classes))
        
        self.classifier = nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.classifier(x)


class IntentEncoder(nn.Module):
    """Intent classifier with PEFT LoRA adapter support."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Model configuration
        model_config = config['model']
        self.encoder_name = model_config['encoder_name']
        self.max_sequence_length = model_config['max_sequence_length']
        self.fine_tune_encoder = model_config.get('fine_tune_encoder', True)
        self.freeze_bottom_layers = model_config.get('freeze_bottom_layers', 0)
        
        # Adapter configuration
        adapter_config = model_config.get('adapter', {})
        self.adapter_enabled = adapter_config.get('enabled', False) and PEFT_AVAILABLE
        self.adapter_type = adapter_config.get('adapter_type', 'none')
        
        # Pooling configuration
        self.pooling_type = model_config.get('pooling_type', 'cls')
        
        # Load tokenizer and encoder
        self.tokenizer = AutoTokenizer.from_pretrained(self.encoder_name)
        self.encoder = AutoModel.from_pretrained(self.encoder_name)
        
        # Setup adapters if enabled
        if self.adapter_enabled:
            self._setup_adapters()
        else:
            # Standard fine-tuning setup
            if not self.fine_tune_encoder:
                # Freeze encoder
                for param in self.encoder.parameters():
                    param.requires_grad = False
            elif self.freeze_bottom_layers > 0:
                # Freeze bottom layers
                self._freeze_bottom_layers()
        
        # Setup pooling
        if self.pooling_type == "attention":
            self.attention_pooling = AttentionPooling(self.encoder.config.hidden_size)
        else:
            self.attention_pooling = None
        
        # Initialize classifier head (will be set later)
        self.classifier = None
        self.loss_fn = None
        
        # Move to device
        self.to(self.device)
        
        # Log initialization
        logger.info(f"IntentEncoder initialized with {self.pooling_type} pooling")
        logger.info(f"Fine-tuning encoder: {self.fine_tune_encoder}")
        if self.adapter_enabled:
            logger.info(f"Adapter-based fine-tuning: {self.adapter_type}")
        else:
            logger.info("Standard fine-tuning enabled")
        
        if self.freeze_bottom_layers > 0:
            logger.info(f"Frozen bottom layers: {self.freeze_bottom_layers}")
    
    def set_loss_function(self, loss_fn):
        """Set the loss function for training."""
        self.loss_fn = loss_fn
    
    def get_loss_function(self):
        """Get the loss function, defaulting to CrossEntropyLoss if not set."""
        if self.loss_fn is None:
            return nn.CrossEntropyLoss()
        return self.loss_fn
    
    def _setup_adapters(self):
        """Setup PEFT LoRA adapters with BERT-based target modules."""
        if not self.adapter_enabled or not PEFT_AVAILABLE:
            return
            
        adapter_config = self.config['model']['adapter']['adapter_config']
        
        try:
            if self.adapter_type == "lora":
                lora_config = adapter_config.get('lora', {})
                
                # BERT-based target modules for BGE model
                target_modules = [
                    "attention.self.query",
                    "attention.self.key", 
                    "attention.self.value",
                    "attention.output.dense",
                    "intermediate.dense",
                    "output.dense"
                ]
                
                peft_config = LoraConfig(
                    task_type=TaskType.SEQ_CLS,
                    inference_mode=False,
                    r=lora_config.get('rank', 8),
                    lora_alpha=lora_config.get('alpha', 32),
                    lora_dropout=lora_config.get('dropout', 0.1),
                    bias="none",
                    target_modules=target_modules
                )
                
                self.encoder = get_peft_model(self.encoder, peft_config)
                logger.info(f"LoRA adapter configured with BERT-based modules: {target_modules}")
                
                # Freeze base model parameters
                for name, param in self.encoder.named_parameters():
                    if "adapter" not in name and "lora" not in name:
                        param.requires_grad = False
                
                logger.info("Base model frozen, only adapters will be fine-tuned")
                        
            elif self.adapter_type == "bitfit":
                # BitFit is not available in this PEFT version, fallback to LoRA
                logger.warning("BitFit not available, using LoRA adapter instead")
                self.adapter_type = "lora"
                self._setup_adapters()  # Recursive call with lora
                
        except Exception as e:
            logger.warning(f"PEFT adapter setup failed: {e}. Falling back to standard fine-tuning.")
            self.adapter_enabled = False
            # Re-enable all parameters for standard fine-tuning
            for name, param in self.encoder.named_parameters():
                param.requires_grad = True
    
    def _freeze_bottom_layers(self):
        """Freeze bottom layers of the encoder."""
        if hasattr(self.encoder, 'encoder') and hasattr(self.encoder.encoder, 'layer'):
            for i, layer in enumerate(self.encoder.encoder.layer):
                if i < self.freeze_bottom_layers:
                    for param in layer.parameters():
                        param.requires_grad = False
    
    def set_num_classes(self, num_classes: int):
        """Set number of classes and initialize classifier head."""
        classifier_config = self.config['model']['classifier_head']
        self.classifier = ImprovedClassifierHead(
            input_dim=self.encoder.config.hidden_size,
            hidden_dims=classifier_config['layers'],
            num_classes=num_classes,
            dropout_rates=classifier_config['dropout_rates'],
            activation=classifier_config['activation'],
            use_batch_norm=classifier_config['use_batch_norm']
        ).to(self.device)
        
        # Add loss function
        self.loss_fn = nn.CrossEntropyLoss()
    
    def _pool_embeddings(self, hidden_states: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Apply pooling strategy to get sentence embeddings."""
        if self.pooling_type == "cls":
            # Use CLS token
            return hidden_states[:, 0]
        elif self.pooling_type == "mean":
            # Mean pooling
            if attention_mask is not None:
                # Mask padded tokens
                mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size()).float()
                sum_embeddings = torch.sum(hidden_states * mask_expanded, dim=1)
                sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
                return sum_embeddings / sum_mask
            else:
                return torch.mean(hidden_states, dim=1)
        elif self.pooling_type == "attention":
            # Attention pooling
            if self.attention_pooling:
                return self.attention_pooling(hidden_states, attention_mask)
            else:
                return hidden_states[:, 0]  # Fallback to CLS
        else:
            return hidden_states[:, 0]  # Default to CLS
    
    def forward(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None,
                return_embeddings: bool = False, **kwargs) -> Dict[str, torch.Tensor]:
        """Forward pass through the model."""
        # Call base model directly to avoid PEFT argument forwarding issues
        if hasattr(self.encoder, 'base_model'):
            # PEFT model - call base model directly
            outputs = self.encoder.base_model(input_ids=input_ids, attention_mask=attention_mask)
        else:
            # Regular model
            outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask)
        
        hidden_states = outputs.last_hidden_state
        
        # Apply pooling
        embeddings = self._pool_embeddings(hidden_states, attention_mask)
        
        # Classify
        if self.classifier is not None:
            logits = self.classifier(embeddings)
        else:
            # Return embeddings if classifier not set
            logits = embeddings
        
        if return_embeddings:
            return {'logits': logits, 'embeddings': embeddings}
        return {'logits': logits}
    
    def predict(self, texts: List[str], batch_size: int = 32) -> tuple:
        """Predict intents for a list of texts."""
        self.eval()
        predictions = []
        probabilities = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            
            # Tokenize
            encoding = self.tokenizer(
                batch_texts,
                truncation=True,
                padding=True,
                max_length=self.max_sequence_length,
                return_tensors='pt'
            )
            
            with torch.no_grad():
                outputs = self.forward(
                    input_ids=encoding['input_ids'].to(self.device),
                    attention_mask=encoding['attention_mask'].to(self.device)
                )
                
                probs = torch.softmax(outputs['logits'], dim=1)
                preds = torch.argmax(outputs['logits'], dim=1)
                
                predictions.extend(preds.cpu().numpy())
                probabilities.extend(probs.cpu().numpy())
        
        return predictions, probabilities
    
    def get_embeddings(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
        """Get embeddings for a list of texts."""
        self.eval()
        embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            
            # Tokenize
            encoding = self.tokenizer(
                batch_texts,
                truncation=True,
                padding=True,
                max_length=self.max_sequence_length,
                return_tensors='pt'
            )
            
            with torch.no_grad():
                outputs = self.forward(
                    input_ids=encoding['input_ids'].to(self.device),
                    attention_mask=encoding['attention_mask'].to(self.device),
                    return_embeddings=True
                )
                
                embeddings.extend(outputs['embeddings'].cpu().numpy())
        
        return np.array(embeddings)
    
    def save_model(self, save_dir: str):
        """Save the model."""
        import os
        from pathlib import Path
        
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Save model state
        torch.save(self.state_dict(), save_path / "model.pth")
        
        # Save tokenizer
        self.tokenizer.save_pretrained(save_path)
        
        # Save adapter if enabled
        if self.adapter_enabled:
            self.encoder.save_pretrained(save_path / "adapters")
        
        # Save config
        import json
        with open(save_path / "config.json", 'w') as f:
            json.dump(self.config, f, indent=2)
        
        logger.info(f"Model saved to {save_path}")
    
    def load_model(self, load_dir: str):
        """Load the model."""
        import os
        from pathlib import Path
        
        load_path = Path(load_dir)
        
        # Load model state
        if (load_path / "model.pth").exists():
            self.load_state_dict(torch.load(load_path / "model.pth", map_location=self.device))
        
        # Load tokenizer
        if (load_path / "tokenizer.json").exists():
            self.tokenizer = AutoTokenizer.from_pretrained(load_path)
        
        # Load adapter if enabled
        if self.adapter_enabled and (load_path / "adapters").exists():
            self.encoder = self.encoder.from_pretrained(load_path / "adapters")
        
        logger.info(f"Model loaded from {load_path}")


class TwoStageIntentClassifier(nn.Module):
    """Two-stage intent classifier for domain-specific classification."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Stage 1: Domain classifier
        self.domain_classifier = IntentEncoder(config)
        
        # Stage 2: Intent classifiers (one per domain)
        self.intent_classifiers = {}
        stage2_config = config['model']['two_stage']
        
        for domain in stage2_config['stage1_domains']:
            domain_config = config.copy()
            domain_config['model']['encoder_name'] = stage2_config['stage2_models_dir'] + f"/{domain}_classifier"
            self.intent_classifiers[domain] = IntentEncoder(domain_config)
        
        self.to(self.device)
    
    def forward(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None,
                domain_labels: Optional[torch.Tensor] = None, intent_labels: Optional[torch.Tensor] = None):
        """Forward pass through both stages."""
        # Stage 1: Domain classification
        domain_outputs = self.domain_classifier(input_ids, attention_mask)
        
        # Stage 2: Intent classification (if domain is provided)
        intent_outputs = None
        if domain_labels is not None:
            # This would require more complex logic for batch processing
            # For now, we'll return domain outputs
            pass
        
        return {
            'domain_logits': domain_outputs['logits'],
            'intent_logits': intent_outputs['logits'] if intent_outputs else None
        }
    
    def predict(self, texts: List[str], batch_size: int = 32) -> Dict[str, Any]:
        """Predict domains and intents."""
        # Stage 1: Domain prediction
        domain_predictions, domain_probabilities = self.domain_classifier.predict(texts, batch_size)
        
        # Stage 2: Intent prediction (simplified)
        intent_predictions = []
        intent_probabilities = []
        
        for i, domain_pred in enumerate(domain_predictions):
            # Get the domain name
            domain_name = self.config['model']['two_stage']['stage1_domains'][domain_pred]
            
            # Predict intent using domain-specific classifier
            if domain_name in self.intent_classifiers:
                intent_pred, intent_prob = self.intent_classifiers[domain_name].predict([texts[i]], 1)
                intent_predictions.append(intent_pred[0])
                intent_probabilities.append(intent_prob[0])
            else:
                # Fallback
                intent_predictions.append(0)
                intent_probabilities.append([1.0] + [0.0] * 9)  # Dummy probabilities
        
        return {
            'domains': domain_predictions,
            'domain_probabilities': domain_probabilities,
            'intents': intent_predictions,
            'intent_probabilities': intent_probabilities
        } 