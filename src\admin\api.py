from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from typing import List
from src.admin.models import AdminUser, Role, Permission, RolePermission, AuditLog
from src.admin.dependencies import get_db, get_current_admin, require_permission, enforce_role_level
from src.admin.utils.audit import log_audit_action
from src.admin.payroll_api import router as payroll_router
from datetime import datetime
import os
from src.utils.payslip_data import get_payslip_record

router = APIRouter()

# Include payroll routes
router.include_router(payroll_router)

# --- GET /api/admin-users ---
@router.get("/api/admin-users", response_model=List[dict])
def list_admin_users(db: Session = Depends(get_db), current_user: AdminUser = Depends(get_current_admin)):
    users = db.query(AdminUser).all()
    result = []
    for user in users:
        role = db.query(Role).filter(Role.id == user.role_id).first()
        result.append({
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "role": role.name if role else None,
            "role_level": role.level if role else None,
            "tenant_id": user.tenant_id,
            "created_at": user.created_at,
            "updated_at": user.updated_at
        })
    return result

# --- POST /api/admin-users ---
@router.post("/api/admin-users", status_code=201)
def add_admin_user(payload: dict, request: Request, db: Session = Depends(get_db), current_user: AdminUser = Depends(get_current_admin)):
    enforce_role_level(current_user, payload["role_id"], db)
    require_permission(current_user, "manage_users", db)
    # Check if email exists
    if db.query(AdminUser).filter(AdminUser.email == payload["email"]).first():
        raise HTTPException(status_code=400, detail="Email already exists.")
    user = AdminUser(
        name=payload["name"],
        email=payload["email"],
        role_id=payload["role_id"],
        tenant_id=payload.get("tenant_id"),
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    log_audit_action(db, actor_id=current_user.id, target_user_id=user.id, action="create_user", details=f"Created user {user.email}", ip_address=request.client.host, tenant_id=user.tenant_id)
    return {"success": True, "id": user.id}

# --- PATCH /api/admin-users/{id} ---
@router.patch("/api/admin-users/{id}")
def update_admin_user(id: int, payload: dict, request: Request, db: Session = Depends(get_db), current_user: AdminUser = Depends(get_current_admin)):
    user = db.query(AdminUser).filter(AdminUser.id == id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    enforce_role_level(current_user, payload["role_id"], db)
    require_permission(current_user, "manage_users", db)
    old_role_id = user.role_id
    user.role_id = payload["role_id"]
    user.updated_at = datetime.utcnow()
    db.commit()
    log_audit_action(db, actor_id=current_user.id, target_user_id=user.id, action="update_role", details=f"Role changed from {old_role_id} to {user.role_id}", ip_address=request.client.host, tenant_id=user.tenant_id)
    return {"success": True}

# --- DELETE /api/admin-users/{id} ---
@router.delete("/api/admin-users/{id}")
def remove_admin_user(id: int, request: Request, db: Session = Depends(get_db), current_user: AdminUser = Depends(get_current_admin)):
    user = db.query(AdminUser).filter(AdminUser.id == id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    enforce_role_level(current_user, user.role_id, db)
    require_permission(current_user, "manage_users", db)
    db.delete(user)
    db.commit()
    log_audit_action(db, actor_id=current_user.id, target_user_id=id, action="remove_user", details=f"Removed user {user.email}", ip_address=request.client.host, tenant_id=user.tenant_id)
    return {"success": True}

# --- GET /api/permissions ---
@router.get("/api/permissions", response_model=List[dict])
def list_permissions(db: Session = Depends(get_db), current_user: AdminUser = Depends(get_current_admin)):
    perms = db.query(Permission).all()
    return [{"id": p.id, "key": p.key, "description": p.description} for p in perms]

# --- GET /api/roles ---
@router.get("/api/roles")
def list_roles(db: Session = Depends(get_db), current_user: AdminUser = Depends(get_current_admin)):
    roles = db.query(Role).all()
    return [
        {"id": r.id, "name": r.name, "level": r.level}
        for r in roles
    ]

# --- POST /api/audit-logs ---
@router.post("/api/audit-logs")
def create_audit_log(payload: dict, db: Session = Depends(get_db), current_user: AdminUser = Depends(get_current_admin)):
    log = AuditLog(
        created_by=current_user.id,
        target_user=payload.get("target_user_id"),
        action=payload["action"],
        details=payload.get("details"),
        ip_address=payload.get("ip_address"),
        tenant_id=payload.get("tenant_id"),
        timestamp=datetime.utcnow()
    )
    db.add(log)
    db.commit()
    return {"success": True, "id": log.id}

@router.get("/api/payslips")
def get_payslip(
    employee_id: int = Query(..., description="Employee ID"),
    email: str = Query(..., description="Employee email"),
    month: str = Query(..., description="Month, e.g. June"),
    year: int = Query(..., description="Year, e.g. 2025")
):
    # Validate inputs
    if not (employee_id and email and month and year):
        raise HTTPException(status_code=400, detail="All parameters are required.")
    # Normalize month
    month_norm = month.strip().capitalize()
    # Fetch payslip record
    payslip = get_payslip_record(employee_id, email, month_norm, year)
    if payslip:
        base_url = os.getenv("SALARY_SLIP_URL", "https://example.com/payslips")
        download_url = f"{base_url}/{payslip['filename']}"
        return {"download_url": download_url}
    raise HTTPException(status_code=404, detail="Salary slip not available for the given period.") 