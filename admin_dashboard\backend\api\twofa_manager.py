import pyotp
import qrcode
from io import BytesIO

def generate_secret(email: str) -> str:
    return pyotp.random_base32()

def get_qr_code(email: str, secret: str) -> BytesIO:
    totp = pyotp.TOTP(secret)
    uri = totp.provisioning_uri(name=email, issuer_name="Ziantrix Admin")
    img = qrcode.make(uri)
    buf = BytesIO()
    img.save(buf)
    buf.seek(0)
    return buf

def is_2fa_enabled(email: str) -> bool:
    # This function will need to be updated to fetch user data from a chatbot backend
    # For now, it will return False as UserModel is removed.
    return False 