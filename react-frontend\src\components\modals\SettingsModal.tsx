import React, { useState, useEffect } from 'react';
import { User, ThemeMode } from '@/types';
import { userAPI } from '@/utils/api';

interface SettingsModalProps {
  onClose: () => void;
  theme: ThemeMode['mode'];
  onThemeChange: (theme: ThemeMode['mode']) => void;
  user: User | null;
  onUpdateUser: (updates: Partial<User>) => void;
  onLogout: () => void;
  onClearAllChats: () => void;
  onOpenArchivedChats: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  onClose,
  theme,
  onThemeChange,
  user,
  onUpdateUser,
  onLogout,
  onClearAllChats,
  onOpenArchivedChats,
}) => {
  const [activeTab, setActiveTab] = useState<'general' | 'security' | 'personal'>('general');
  const [personalInfo, setPersonalInfo] = useState({
    fullName: user?.fullName || '',
    email: user?.email || '',
    employeeId: user?.employeeId || '',
  });
  const [isSaving, setIsSaving] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

  // Load user profile data when modal opens or user changes
  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        const profileData = await userAPI.getProfile();
        setPersonalInfo({
          fullName: profileData.full_name || user?.fullName || '',
          email: profileData.email || user?.email || '',
          employeeId: profileData.employee_id || user?.employeeId || '',
        });
      } catch (error) {
        console.error('Failed to load user profile:', error);
        // Fallback to user prop data
        setPersonalInfo({
          fullName: user?.fullName || '',
          email: user?.email || '',
          employeeId: user?.employeeId || '',
        });
      }
    };

    loadUserProfile();
  }, [user]);

  const handlePersonalInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPersonalInfo(prev => ({ ...prev, [name]: value }));
  };

  const handleSavePersonalInfo = async () => {
    setIsSaving(true);
    try {
      await userAPI.updateProfile({
        full_name: personalInfo.fullName,
        email: personalInfo.email,
        employee_id: personalInfo.employeeId,
      });
      onUpdateUser(personalInfo);
      setToast({ message: 'Profile updated successfully!', type: 'success' });
    } catch (e) {
      setToast({ message: 'Failed to update profile. Please try again.', type: 'error' });
    } finally {
      setIsSaving(false);
      setTimeout(() => setToast(null), 3000);
    }
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const confirmAction = (action: () => void, message: string) => {
    if (window.confirm(message)) {
      action();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content max-w-2xl relative">
        {toast && (
          <div className={`absolute top-4 right-4 z-50 px-4 py-2 rounded shadow-lg transition-all duration-300 ${toast.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`}>{toast.message}</div>
        )}
        <div className="flex items-center justify-between p-6 border-b border-primary-border dark:border-dark-border">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Settings</h3>
          <button onClick={onClose} className="icon-btn">
            <i className="fas fa-times text-gray-500 dark:text-gray-400"></i>
          </button>
        </div>
        <div className="flex">
          <div className="w-48 border-r border-primary-border dark:border-dark-border">
            <nav className="p-4 space-y-2">
              {[
                { id: 'general', icon: 'fas fa-cog', label: 'General' },
                { id: 'security', icon: 'fas fa-shield-alt', label: 'Security' },
                { id: 'personal', icon: 'fas fa-user', label: 'Personal' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors duration-200 ${activeTab === tab.id ? 'bg-primary-accent dark:bg-dark-accent text-gray-900 dark:text-gray-100' : 'text-gray-900 dark:text-gray-100 hover:bg-primary-secondary dark:hover:bg-dark-secondary'}`}
                >
                  <i className={tab.icon}></i>
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
          <div className="flex-1 p-6">
            {activeTab === 'general' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <span className="text-gray-900 dark:text-gray-100">Theme</span>
                  <select value={theme} onChange={(e) => onThemeChange(e.target.value as ThemeMode['mode'])} className="form-input w-32">
                    <option value="system">System</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                  </select>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-900 dark:text-gray-100">Language</span>
                  <select className="form-input w-32">
                    <option value="auto-detect">Auto-detect</option>
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                  </select>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-900 dark:text-gray-100">Archived chats</span>
                  <button onClick={onOpenArchivedChats} className="btn-secondary">Manage</button>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-900 dark:text-gray-100">Delete all chats</span>
                  <button onClick={() => confirmAction(onClearAllChats, 'Are you sure you want to delete all chats? This action cannot be undone.')} className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200">Delete all</button>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-900 dark:text-gray-100">Log out on this device</span>
                  <button onClick={onLogout} className="btn-secondary">Log out</button>
                </div>
              </div>
            )}
            {activeTab === 'security' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-gray-900 dark:text-gray-100">Multi-factor authentication</div>
                    <p className="text-sm text-gray-500 dark:text-gray-300 mt-1">Require an extra security challenge when logging in.</p>
                  </div>
                  <button className="btn-secondary">Enable</button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-gray-900 dark:text-gray-100">Log out of all devices</div>
                    <p className="text-sm text-gray-500 dark:text-gray-300 mt-1">Log out of all active sessions across all devices.</p>
                  </div>
                  <button className="btn-secondary">Log out all</button>
                </div>
              </div>
            )}
            {activeTab === 'personal' && (
              <div className="space-y-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Personal Information</h4>
                <div className="space-y-4">
                  <div className="form-group">
                    <label htmlFor="personalFullName" className="form-label text-gray-900 dark:text-gray-100">Full Name</label>
                    <input type="text" id="personalFullName" name="fullName" value={personalInfo.fullName} onChange={handlePersonalInfoChange} className="form-input" placeholder="Enter your full name" />
                  </div>
                  <div className="form-group">
                    <label htmlFor="personalEmail" className="form-label text-gray-900 dark:text-gray-100">Email Address</label>
                    <input type="email" id="personalEmail" name="email" value={personalInfo.email} onChange={handlePersonalInfoChange} className="form-input" placeholder="Enter your email" />
                  </div>
                  <div className="form-group">
                    <label htmlFor="personalEmployeeId" className="form-label text-gray-900 dark:text-gray-100">Employee ID</label>
                    <input type="text" id="personalEmployeeId" name="employeeId" value={personalInfo.employeeId} onChange={handlePersonalInfoChange} className="form-input" placeholder="Enter your employee ID" />
                  </div>
                  <div className="flex justify-end">
                    <button onClick={handleSavePersonalInfo} disabled={isSaving} className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed">
                      {isSaving ? (
                        <div className="flex items-center space-x-2">
                          <div className="loading-spinner"></div>
                          <span>Saving...</span>
                        </div>
                      ) : (
                        'Save Changes'
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
